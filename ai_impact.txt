import json

import autogen
from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
import datetime
import autogen
import datetime
import boto3


config_list_bedrock = [
    {
        "api_type": "bedrock",
        "model": "anthropic.claude-3-haiku-20240307-v1:0",
        "aws_region": "us-west-2",
        "aws_access_key": "****",
        "aws_secret_key": "***",
        "price": [0.003, 0.015],
        "temperature": 0.7,
        "cache_seed": None,
    }
]
llm_config = {
        "config_list": config_list_bedrock,
    }
# Initialize user proxy
user_proxy = UserProxyAgent(
    name="user_proxy",
    human_input_mode="NEVER",
    code_execution_config=False,
    max_consecutive_auto_reply=3,
    default_auto_reply="Please continue. If finished, reply 'TERMINATE'.",
)
# Define the agents
JD_GENERATOR = AssistantAgent(
    name="JD_Generator",
    llm_config=llm_config,
    system_message="""
You are an expert in writing professional and highly attractive job descriptions tailored for diverse industries.

### Objective:
Generate a well-structured, clear, and engaging job description for a given role.

### Expected Structure:
- **Job Title**
- **Summary**
- **Key Responsibilities**
- **Required Qualifications**
- **Preferred Qualifications (optional)**

### Style Guidelines:
- Maintain a professional and inclusive tone.
- Use dynamic, action-oriented language.
- Ensure clarity and conciseness.
- Minimize jargon and explain where necessary.
"""
)

JD_INSPECTOR = AssistantAgent(
    name="JD_Inspector",
    llm_config=llm_config,
    system_message="""
You are a job description auditor.

### Validation Rules:
- Ensure all core sections are present.
- Logical alignment between Responsibilities and Qualifications.
- Avoid vague, generic, or immeasurable language.
- Maintain a consistent professional tone.

### Feedback Format:
VALID: [True/False]
ISSUES:
- [Section]: [Identified issue] → [Suggested correction]
"""
)

JD_EDITOR = AssistantAgent(
    name="JD_Editor",
    llm_config=llm_config,
    system_message="""
You are a precise job description editor.

### Editing Rules:
- Apply only the listed corrections.
- Do not modify validated sections.
- Return a fully corrected and clean job description without additional comments.

### Output Format:
{
  "job_description": "[Corrected job description]",
  "changes_made": ["[List of changes applied]"]
}
"""
)

ROLE_ANALYSIS_GENERATOR = AssistantAgent(
    name="RoleAnalysis_Generator",
    llm_config=llm_config,
    system_message="""
You are an expert in comprehensive job role analysis.

### Objective:
Based on a given Job Description, generate a single structured JSON output containing:
- CORE ACTIVITIES
- DECISION POINTS
- KNOWLEDGE REQUIREMENTS

### Expected Output:
{
  "core_activities": ["Activity 1", "Activity 2", ...],
  "decision_points": ["Decision 1", "Decision 2", ...],
  "knowledge_requirements": ["Knowledge area 1", "Knowledge area 2", ...]
}

### Guidelines:
- Use professional, action-driven, measurable language.
- Ensure all outputs are concise, clear, and highly relevant to the role.
- Maintain strict JSON format.
"""
)
VECTOR_INSPECTOR = AssistantAgent(
    name="VectorInspector",
    llm_config=llm_config,
    system_message="""
You are a JSON vector structure auditor.

### Objective:
Inspect the JSON output for CORE ACTIVITIES, DECISION POINTS, and KNOWLEDGE REQUIREMENTS.

### Validation Rules:
- Check for missing fields or incorrect structure.
- Ensure items are clear, relevant, and non-redundant.
- Maintain a professional and measurable style.

### Feedback Format:
{
  "valid": [true/false],
  "issues": [
    {"section": "Section Name", "problem": "Identified issue", "suggestion": "Suggested correction"}
  ]
}
"""
)

VECTOR_EDITOR = AssistantAgent(
    name="VectorEditor",
    llm_config=llm_config,
    system_message="""
You are a precise JSON vector structure editor.

### Objective:
Apply only the listed corrections to the JSON structure.

### Output Format:
{
  "corrected_vectors": {
    "core_activities": [...],
    "decision_points": [...],
    "knowledge_requirements": [...]
  },
  "changes_made": ["[List of changes applied]"]
}

### Additional Rule:
Do not include any comment, explanation, or text before or after the JSON. Output only the JSON object as specified.
"""
)


AUTOMATION_ANALYZER = AssistantAgent(
    name="Automation_Analyzer",
    llm_config=llm_config,
    system_message="""
You are an expert in AI automation potential analysis. Your task is to analyze activities and classify their automation potential.

### Input:
- List of core activities in JSON format

### Output Format (STRICT JSON):
{
  "automation_analysis": [
    {
      "activity": "original activity text",
      "automation_potential": "full/partial/none", 
      "ai_techniques": ["list of applicable AI techniques"],
      "rationale": "brief explanation"
    }
  ]
}

### Guidelines:
1. 'full': Activity can be 100% automated by current AI
2. 'partial': Activity can be significantly assisted by AI but requires human input
3. 'none': Activity requires human judgment/creativity
4. Always suggest specific AI techniques (e.g., "computer vision", "LLMs")
5. Keep rationale concise and technical
"""
)
AUTOMATION_VERIFIER = AssistantAgent(
    name="Automation_Verifier",
    llm_config=llm_config,
    system_message="""
You are a quality assurance specialist for AI automation analysis. Your task is to verify and correct automation potential assessments.

### Input:
- JSON output from Automation_Analyzer

### Verification Rules:
1. Validate "automation_potential" values (must be: full/partial/none)
2. Ensure each activity has:
   - Non-empty "ai_techniques" list
   - Valid technical rationale
   - Consistent classification
3. Correct any over/under-estimated automation potential
4. Add missing AI techniques when obvious

### Output Format (STRICT JSON):
{
  "automation_analysis": [
    {
      "activity": "original text",
      "automation_potential": "corrected_value",
      "ai_techniques": ["validated_list"],
      "rationale": "improved_explanation",
      "correction_made": true/false
    }
  ],
  "validation_summary": {
    "total_activities": X,
    "corrected_items": Y,
    "confidence_score": "high/medium/low"
  }
}

### Guidelines:
- Never change the original activity text
- Only modify other fields when clearly justified
- Maintain same JSON structure as input
"""
)
DECISION_ANALYZER = AssistantAgent(
    name="Decision_Analyzer",
    llm_config=llm_config,
    system_message="""
You analyze decision points for AI augmentation potential.

### Input:
- List of decision points in JSON format

### Output Format (STRICT JSON):
{
  "decision_analysis": [
    {
      "decision_point": "original text",
      "ai_aid_level": "high/medium/low",
      "supporting_ai": ["specific AI techniques"],
      "human_role": "required human involvement"
    }
  ]
}
"""
)
def get_last_relevant_message(all_messages,agent_names):
    for i in range(1, len(all_messages) + 1):
        message = all_messages[-i]
        if message["name"] in agent_names:
            return message["content"]
    return None


def job_description_generator(job_title):
    prompt = f"Please generate a professional job description for the following position: {job_title}"

    group_chat = GroupChat(
        agents=[JD_GENERATOR, JD_INSPECTOR, JD_EDITOR],
        messages=[],
        max_round=4
    )
    manager = GroupChatManager(groupchat=group_chat, llm_config=llm_config)

    user_proxy.initiate_chat(manager, message=prompt, silent=True)
    all_messages = manager.groupchat.messages
    response = get_last_relevant_message(all_messages, ["JD_Generator", "JD_Editor"])
    response_json = json.loads(response.replace("\n"," "))
    return response_json["job_description"]


def process_job_description(job_description):
    prompt = f"""
    Based on the following Job Description, generate a single structured JSON containing:
    - CORE ACTIVITIES
    - DECISION POINTS
    - KNOWLEDGE REQUIREMENTS

    Ensure outputs are professional, action-driven, and strictly follow JSON format.

    Job Description:
    {job_description}
    """

    group_chat = GroupChat(
        agents=[ROLE_ANALYSIS_GENERATOR, VECTOR_INSPECTOR, VECTOR_EDITOR],
        messages=[],
        max_round=4,
        speaker_selection_method="round_robin",
        allow_repeat_speaker=False
    )
    manager = GroupChatManager(groupchat=group_chat, llm_config=llm_config)
    json_status = False
    iter = 0
    response_json = {'corrected_vectors':{"core_activities":None,"decision_points":None,"knowledge_requirements":None}}
    while iter <3 and not json_status:
        iter += 1
        user_proxy.initiate_chat(manager, message=prompt, silent=True)
        all_messages = manager.groupchat.messages
        response = get_last_relevant_message(all_messages, ["RoleAnalysis_Generator", "VectorEditor"])
        try:
            json_status = True
            response_json = json.loads(response)
        except Exception as e:
            print(str(e))
            print(response)
            json_status = False
    print(json_status)
    core_activities = response_json['corrected_vectors']["core_activities"]
    decision_points = response_json['corrected_vectors']["decision_points"]
    knowledge_requirements = response_json['corrected_vectors']["knowledge_requirements"]
    return core_activities, decision_points, knowledge_requirements


def analyze_automation_potential(core_activities):

    input_json = json.dumps({"activities": core_activities})

    group_chat = GroupChat(
        agents=[AUTOMATION_ANALYZER, AUTOMATION_VERIFIER],
        messages=[],
        max_round=3,
        speaker_selection_method="round_robin"
    )

    manager = GroupChatManager(
        groupchat=group_chat,
        llm_config=llm_config
        #is_termination_msg=lambda x: x.get("content", "").strip().startswith("{")
    )

    user_proxy = UserProxyAgent(name="UserProxy",
                                human_input_mode = "NEVER",
                                code_execution_config = False,
                                )
    user_proxy.initiate_chat(
        manager,
        message=input_json,
        silent=True
    )

    final_message = next(
        msg for msg in reversed(group_chat.messages)
        if msg["name"] == "Automation_Verifier"
    )

    try:
        return json.loads(final_message["content"])
    except json.JSONDecodeError:
        print("Failed to parse JSON output")
        return {
            "verified_automation": [],
            "validation_summary": {
                "total_activities": 0,
                "corrected_items": 0,
                "confidence_score": "low"
            }
        }

def analyser(job_title):
    job_description = job_description_generator(job_title)
    core_activities, decision_points, knowledge_requirements = process_job_description(job_description)
    AI_core_activities = analyze_automation_potential(core_activities)["automation_analysis"]
    partial_count = sum(1 for activity in AI_core_activities
                        if activity.get("automation_potential") == "partial")
    full_count = sum(1 for activity in AI_core_activities
                        if activity.get("automation_potential") == "full")
    ai_impact = (partial_count+full_count)/len(AI_core_activities)
    return ai_impact