"""
AutoGen Multi-Agent Job Analysis System (AWS Bedrock-Powered)
------------------------------------------------------------
This script implements a fully AI-driven multi-agent system using the AutoGen framework and AWS Bedrock (Anthropic Claude) for all agent outputs. The system analyzes a user-provided job title or role, producing dynamic, AI-generated results for job description, task extraction, AI assessment, automation categorization, and workforce sustainability recommendations.

Agents:
1. Description Agent (<PERSON> via Bedrock)
2. Task Extraction Agent (<PERSON> via Bedrock)
3. AI Assessment Agent (<PERSON> via Bedrock)
4. AI Categorizer Agent (<PERSON> via Bedrock)
5. Recommendation Agent (<PERSON> via Bedrock)

All logic is 100% AI-driven via Bedrock. No mock or default data is used.
"""

import json
import re
from typing import Dict, Any
from autogen import AssistantAgent, UserProxyAgent
from json_repair import repair_json

# -----------------------------
# AWS Bedrock LLM Configuration
# -----------------------------
config_list_bedrock = [
    {
        "api_type": "bedrock",
        "model": "anthropic.claude-3-haiku-20240307-v1:0",
        "aws_region": "us-east-1",
        "aws_access_key": "********************",
        "aws_secret_key": "ghYKGewFG7lnQ+uhBxSGB48r+x2CCxwnpHLrpDbN",
        "temperature": 0.5,
    }
]
llm_config = {
    "config_list": config_list_bedrock,
    "timeout": 120,
    "cache_seed": None
}

# -----------------------------
# Agent Definitions
# -----------------------------
description_agent = AssistantAgent(
    name="Description_Agent",
    system_message="""
    You are an expert in job analysis. Generate a detailed job description for the provided job title.
    Output MUST be a JSON object with:
    - job_title: The job title
    - description: 1-2 sentence overview
    - responsibilities: List of responsibility objects with 'task' and 'priority' (High/Medium/Low)
    - qualifications: List of required qualifications
    Example:
    ```json
    {
        "job_title": "Software Engineer",
        "description": "Develops and maintains software applications.",
        "responsibilities": [{"task": "Write code", "priority": "High"}],
        "qualifications": ["Bachelor's in CS", "3+ years experience"]
    }
    ```
    """,
    llm_config=llm_config
)

task_extraction_agent = AssistantAgent(
    name="Task_Extraction_Agent",
    system_message="""
    Extract specific tasks from a job description. Output MUST be a JSON array of objects with:
    - task_name: Brief task name
    - description: Detailed task description
    Example:
    ```json
    [{"task_name": "Code Development", "description": "Write and maintain code"}]
    ```
    """,
    llm_config=llm_config
)

ai_assessment_agent = AssistantAgent(
    name="AI_Assessment_Agent",
    system_message="""
    Analyze tasks for AI involvement potential. Output MUST be a JSON array of objects with:
    - task: Task name
    - ai_involvement: "None"/"Partial"/"Full"
    - rationale: Explanation for AI involvement level
    Example:
    ```json
    [{"task": "Code Development", "ai_involvement": "Partial", "rationale": "AI can assist with code generation"}]
    ```
    """,
    llm_config=llm_config
)

ai_categorizer_agent = AssistantAgent(
    name="AI_Categorizer_Agent",
    system_message="""
    Classify tasks based on automation potential. Output MUST be a JSON array of objects with:
    - task: Task name
    - category: "No Automation"/"Partial Automation"/"Full Automation"
    - percentage: Estimated automation percentage (0-100)
    - explanation: Reasoning for classification
    Example:
    ```json
    [{"task": "Code Development", "category": "Partial Automation", "percentage": 60, "explanation": "AI can automate code generation but human oversight needed"}]
    ```
    """,
    llm_config=llm_config
)

recommendation_agent = AssistantAgent(
    name="Recommendation_Agent",
    system_message="""
    Generate recommendations for AI transformation. Output MUST be a JSON object with:
    - ai_impact_analysis: Analysis of AI's impact on the role
    - future_skill_recommendations: List of objects with skill_area, specific_skills, relevance
    - sustainability_plan: List of objects with area, initiative, impact_metric
    - opportunities: List of objects with opportunity, description, value_proposition
    Example:
    ```json
    {
        "ai_impact_analysis": "AI may automate 30% of tasks",
        "future_skill_recommendations": [{"skill_area": "AI Literacy", "specific_skills": ["AI tool usage"], "relevance": "Essential for collaboration"}],
        "sustainability_plan": [{"area": "Upskilling", "initiative": "AI training programs", "impact_metric": "Increased adaptability"}],
        "opportunities": [{"opportunity": "AI Trainer", "description": "Train AI models", "value_proposition": "New revenue stream"}]
    }
    ```
    """,
    llm_config=llm_config
)

# -----------------------------
# Utility Functions
# -----------------------------
def parse_json_safely(json_str: str) -> Dict:
    """Safely parse JSON from agent responses, handling markdown formatting."""
    try:
        match = re.search(r'```(?:json)?\s*(\{.*\}|\[.*\])\s*```', json_str, re.DOTALL)
        if match:
            json_str = match.group(1)
        return json.loads(json_str)
    except json.JSONDecodeError:
        try:
            return json.loads(repair_json(json_str))
        except Exception as e:
            print(f"[ERROR] JSON parsing failed: {e}")
            return {}

def validate_job_position(position: str) -> tuple[bool, str]:
    """Validate job position input."""
    position = position.strip()
    if not position:
        return False, "Job position cannot be empty"
    if len(position) < 3:
        return False, "Job position must be at least 3 characters"
    if len(position) > 100:
        return False, "Job position cannot exceed 100 characters"
    if any(char in '!@#$%^&*()+=[]{}|\\:;"<>?/' for char in position):
        return False, "Job position contains invalid characters"
    return True, ""

def get_agent_response(agent, message: str) -> str:
    """Get a direct response from an agent without using group chat."""
    try:
        # Create a simple user proxy for direct communication
        user_proxy = UserProxyAgent(
            name="User_Proxy",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1,
            llm_config=False  # Don't use LLM for user proxy
        )
        
        # Send message and get response
        user_proxy.initiate_chat(
            agent,
            message=message,
            silent=True
        )
        
        # Get the last message from the agent
        last_message = agent.last_message(user_proxy)
        return last_message.get("content", "")
        
    except Exception as e:
        print(f"[ERROR] Failed to get response from {agent.name}: {e}")
        return ""

# -----------------------------
# Main Analysis Function
# -----------------------------
def analyze_job(job_position: str) -> Dict[str, Any]:
    """
    Analyze a job position using the multi-agent system.
    
    Args:
        job_position (str): The job title to analyze
        
    Returns:
        Dict: Analysis results including job description, tasks, AI assessment, 
              automation categories, and recommendations
    """
    is_valid, error_message = validate_job_position(job_position)
    if not is_valid:
        return {
            "status": "error",
            "message": error_message,
            "job_position": job_position
        }

    try:
        print(f"[INFO] Starting analysis for: {job_position}")
        
        # Step 1: Get job description
        print("[INFO] Step 1: Generating job description...")
        description_response = get_agent_response(
            description_agent, 
            f"Generate a detailed job description for: {job_position}"
        )
        job_description = parse_json_safely(description_response)
        
        if not job_description:
            return {
                "status": "error",
                "message": "Failed to generate job description",
                "job_position": job_position
            }

        # Step 2: Extract tasks
        print("[INFO] Step 2: Extracting tasks...")
        task_response = get_agent_response(
            task_extraction_agent,
            f"Extract tasks from this job description: {json.dumps(job_description)}"
        )
        tasks = parse_json_safely(task_response)
        
        if not tasks:
            return {
                "status": "error",
                "message": "Failed to extract tasks",
                "job_position": job_position
            }

        # Step 3: Assess AI involvement
        print("[INFO] Step 3: Assessing AI involvement...")
        assessment_response = get_agent_response(
            ai_assessment_agent,
            f"Assess AI involvement for these tasks: {json.dumps(tasks)}"
        )
        ai_assessment = parse_json_safely(assessment_response)
        
        if not ai_assessment:
            return {
                "status": "error",
                "message": "Failed to assess AI involvement",
                "job_position": job_position
            }

        # Step 4: Categorize automation potential
        print("[INFO] Step 4: Categorizing automation potential...")
        categorization_response = get_agent_response(
            ai_categorizer_agent,
            f"Categorize automation potential for: {json.dumps(ai_assessment)}"
        )
        automation_categories = parse_json_safely(categorization_response)
        
        if not automation_categories:
            return {
                "status": "error",
                "message": "Failed to categorize automation potential",
                "job_position": job_position
            }

        # Step 5: Generate recommendations
        print("[INFO] Step 5: Generating recommendations...")
        recommendation_response = get_agent_response(
            recommendation_agent,
            f"Generate recommendations for job: {job_position} with automation analysis: {json.dumps(automation_categories)}"
        )
        recommendations = parse_json_safely(recommendation_response)
        
        if not recommendations:
            return {
                "status": "error",
                "message": "Failed to generate recommendations",
                "job_position": job_position
            }

        print("[INFO] Analysis completed successfully!")
        
        # Compile results
        return {
            "status": "success",
            "job_position": job_position,
            "job_description": job_description,
            "tasks": tasks,
            "ai_assessment": ai_assessment,
            "automation_categories": automation_categories,
            "recommendations": recommendations
        }

    except Exception as e:
        print(f"[ERROR] Analysis failed: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "job_position": job_position
        }

# -----------------------------
# Example Usage
# -----------------------------
if __name__ == "__main__":
    # Example usage
    job_title = input("Enter a job title to analyze (e.g., 'Data Analyst'): ").strip()
    result = analyze_job(job_title)
    
    if result["status"] == "success":
        print("\n=== Job Analysis Results ===")
        print(f"Job Position: {result['job_position']}")
        print(f"\nJob Description: {result['job_description'].get('description', 'N/A')}")
        print(f"\nTasks: {len(result['tasks'])} tasks identified")
        print(f"AI Assessment: {len(result['ai_assessment'])} tasks assessed")
        print(f"Automation Categories: {len(result['automation_categories'])} categorized")
        print(f"Recommendations: {len(result['recommendations'])} areas covered")
        
        # Save results to file
        output_file = f"job_analysis_{job_title.replace(' ', '_')}.json"
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"\nResults saved to: {output_file}")
    else:
        print(f"Error: {result['message']}")