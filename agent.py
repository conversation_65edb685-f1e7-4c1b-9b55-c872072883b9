from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
import json
import os
from typing import Dict
import datetime
import re
from json_repair import repair_json
import dirtyjson
from llama_index.core.output_parsers.utils import parse_json_markdown
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import sys
from pydantic import BaseModel
import io
from contextlib import redirect_stdout

# Initialize FastAPI app
app = FastAPI(
    title="AI Job Transformation API",
    description="API for analyzing job positions and their AI transformation potential",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

class JobRequest(BaseModel):
    job_position: str

# API endpoint for job transformation analysis
@app.post("/analyze-job")
async def analyze_job_endpoint(request: JobRequest):
    job_position = request.job_position
    try:
        f = io.StringIO()
        with redirect_stdout(f):
            # Validate job position
            is_valid, error_message = validate_job_position(job_position)
            if not is_valid:
                raise HTTPException(status_code=400, detail=error_message)

            # Get transformation results
            transformation_results = analyze_job_for_ai_transformation(job_position)

            if transformation_results.get("status") == "error":
                raise HTTPException(
                    status_code=500,
                    detail=f"Analysis failed: {transformation_results.get('message')}"
                )
        backend_logs = f.getvalue()
        transformation_results["backend_logs"] = backend_logs
        return transformation_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



# Configuration
config_list_bedrock = [
    {
        "api_type": "bedrock",
        "model": "anthropic.claude-3-haiku-20240307-v1:0",
        "aws_region": "us-east-1",
        "aws_access_key": "********************",
        "aws_secret_key": "ghYKGewFG7lnQ+uhBxSGB48r+x2CCxwnpHLrpDbN",
        "temperature": 0.5, 
    }
]
llm_config = {
    "config_list": config_list_bedrock,
}

# Agents
agent_job_describer = AssistantAgent(
    name="Job_Describer",
    system_message="""
    You are an expert in job analysis and description.
    Your role is to:
    1. Provide a concise, clear description of the job position.
    2. Highlight key responsibilities and requirements.
    3. Explain the role's importance in the organization.

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.
    The job description should follow this exact structure:
    
    ```json
        {
            "job_title": "Title of the position",
            "Description": "Brief 1-2 sentence overview of the role",
            "key_responsibilities": [
                {
                    "responsibility": "Core responsibility 1",
                    "importance": "High/Medium/Low"
                },
                {
                    "responsibility": "Core responsibility 2", 
                    "importance": "High/Medium/Low"
                }
            ],
            "essential_requirements": [
                "Key requirement 1",
                "Key requirement 2"
            ]
        }
    ```
    """,
    llm_config=llm_config
)

agent_task_extractor = AssistantAgent(
    name="Task_Extractor",
    system_message="""
    You are an expert in task extraction from job descriptions.
    Your role is to:
    1. Extract all tasks from a job description.
    2. Be comprehensive and detailed for each task.
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each task extraction should follow this exact structure:
    
    ```json
    [
        {
            "task_name": "Task description",
            "description": "Detailed description of the task"
        }
    ]
    ```
    """,
    llm_config=llm_config
)

agent_task_automation_categorizer = AssistantAgent(
    name="Task_Automation_Categorizer",
    system_message="""
    You are an expert in AI automation analysis. Your role is to categorize job tasks based on their automation potential.
    
    Your task is to:
    1. Analyze each job task and determine if it can be:
       a) FULLY AUTOMATED by AI (90-100% automation)
       b) PARTIALLY AUTOMATED/HYBRID by AI (30-89% automation)
       c) NOT AUTOMATABLE (0-29% automation)
    2. Provide a detailed explanation for each categorization.
    3. Estimate the percentage of AI involvement for each task.

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.
    The output should follow this exact structure:
    
    ```json
    {
        "automation_analysis": [
            {
                "task": "Task description",
                "automation_category": "FULLY_AUTOMATED",
                "ai_involvement_percentage": 100,
                "explanation": "Detailed explanation of why this task can be fully automated."
            }
        ]
    }
    ```
    """,
    llm_config=llm_config
)

html_generator = AssistantAgent(
    name="HTML_Generator",
    system_message="""
    You are an expert HTML visualization generator. Your role is to create a clean, readable, and responsive HTML report based on a provided JSON object.

    Your task is to generate an HTML report with the following sections, in this specific order:
    1.  **Job Description**: Display the title, summary, and key responsibilities.
    2.  **Extracted Tasks**: List all tasks identified from the job description.
    3.  **AI Task Analysis**: For each task, show its name, its AI automation category (e.g., Fully Automated, Partially Automated/Hybrid), the percentage of AI involvement, and the explanation for the analysis.
    4.  **Future Skill Recommendations**: Detail the new skills required for the AI-enhanced role.
    5.  **Workforce Sustainability Plan**: Outline the strategy for maintaining a resilient and adaptable workforce amidst AI integration.
    6.  **New Opportunities**: Describe the new opportunities that arise from AI adoption in this role.

    RULES:
    - The final output MUST be a single HTML string.
    - Use modern HTML5 and self-contained CSS within a `<style>` tag. Do not use external stylesheets or JavaScript.
    - The design must be professional, clean, and easy to read. Use text formats, lists, and tables where appropriate.
    - **Crucially, if any section's data is missing or the list is empty in the input JSON, DO NOT render that section in the HTML report.** For example, if `future_skill_recommendations` is an empty list, the "Future Skill Recommendations" section should be completely omitted from the output.
    - Under no circumstances should you include sections titled "Preserved Tasks", "Transformed Tasks", or "New Capabilities". Only include the sections explicitly listed above.
    - Do not include any explanations or text outside of the HTML content itself.
    """,
    llm_config=llm_config
)

recommendation_agent = AssistantAgent(
    name="Recommendation_Agent",
    system_message="""
    You are an expert in generating strategic recommendations for AI transformation. Your role is to provide actionable plans for future skills, workforce sustainability, and new opportunities.

    Your response MUST be a JSON object containing three keys: `future_skill_recommendations`, `sustainability_plan`, and `opportunities_identification`.

    - **`future_skill_recommendations`**: (List of objects) Detail the skills needed for the AI-enhanced role. Each object should have `skill_area`, `specific_skills`, and `relevance_to_ai_role`.
    - **`sustainability_plan`**: (List of objects) Based on the definition of workforce sustainability (maintaining a resilient, adaptable, and equitable workforce amid AI disruptions), provide initiatives. Each object should have `area`, `initiative`, and `impact_metric`.
    - **`opportunities_identification`**: (List of objects) Identify new business opportunities created by AI adoption. Each object should have `opportunity`, `description`, and `potential_value_proposition`.
    
    Example structure:
    ```json
    {
        "future_skill_recommendations": [
            {
                "skill_area": "Data Literacy and Analysis",
                "specific_skills": ["Understanding AI model outputs", "Data-driven decision making"],
                "relevance_to_ai_role": "Essential for interpreting AI insights."
            }
        ],
        "sustainability_plan": [
            {
                "area": "Continuous Learning and Upskilling",
                "initiative": "Develop personalized learning paths for employees to acquire critical AI-related skills.",
                "impact_metric": "Increased employee retention and internal mobility."
            }
        ],
        "opportunities_identification": [
            {
                "opportunity": "Personalized Customer Experience at Scale",
                "description": "Leverage AI to analyze customer data and provide highly personalized recommendations.",
                "potential_value_proposition": "Increased customer retention by 15%."
            }
        ]
    }
    ```
    """,
    llm_config=llm_config
)

user_proxy = UserProxyAgent(
    name="User_Proxy",
    system_message="Execute tasks and coordinate between agents.",
    human_input_mode="NEVER",
    code_execution_config={"use_docker": False},
    max_consecutive_auto_reply=10,
    default_auto_reply="Please continue. If finished, reply 'TERMINATE'.",
    llm_config=llm_config
)
    
def get_job_description(job_position: str) -> Dict:
    print(f"\n[INFO] Getting job description for: {job_position}")
    user_proxy.initiate_chat(agent_job_describer, message=job_position, silent=True)
    last_message = agent_job_describer.last_message()["content"]
    return parse_json_safely(last_message)

def extract_tasks(job_description: Dict) -> list[Dict]:
    print(f"\n[INFO] Extracting tasks from job description")
    prompt = f"Extract all tasks from this job description:\n{json.dumps(job_description, indent=2)}"
    user_proxy.initiate_chat(agent_task_extractor, message=prompt, silent=True)
    last_message = agent_task_extractor.last_message()["content"]
    return parse_json_safely(last_message)

def categorize_tasks_by_automation(extracted_tasks: list[Dict]) -> Dict:
    print(f"\n[INFO] Categorizing tasks by automation potential")
    prompt = f"Categorize the following tasks by automation potential:\n{json.dumps(extracted_tasks, indent=2)}"
    user_proxy.initiate_chat(agent_task_automation_categorizer, message=prompt, silent=True)
    last_message = agent_task_automation_categorizer.last_message()["content"]
    return parse_json_safely(last_message)

def generate_ai_transition_recommendations(job_description: Dict, automation_analysis: list) -> Dict:
    print(f"\n[INFO] Generating AI transition recommendations")
    prompt = f"""
    Generate recommendations for the AI transformation of a '{job_description.get('job_title', 'N/A')}' role.
    
    Here is the analysis of tasks and their automation potential:
    {json.dumps(automation_analysis, indent=2)}
    
    Based on this, provide recommendations for future skills, a workforce sustainability plan, and new opportunities.
    """
    user_proxy.initiate_chat(recommendation_agent, message=prompt, silent=True)
    last_message = recommendation_agent.last_message()["content"]
    return parse_json_safely(last_message)

def generate_html_report(analysis_results: Dict) -> str:
    print(f"\n[INFO] Generating HTML report for: {analysis_results.get('job_position')}")
    report_data = {key: value for key, value in analysis_results.items() if value}
    
    prompt = f"Generate a comprehensive HTML report based on the following data:\n{json.dumps(report_data, indent=2, default=str)}"
    user_proxy.initiate_chat(html_generator, message=prompt, silent=True)
    html_content = html_generator.last_message()["content"]
    
    if "```html" in html_content:
        html_content = html_content.split("```html")[1].split("```")[0]

    print(f"\n[INFO] Successfully generated HTML report.")
    return html_content.strip()

def analyze_job_for_ai_transformation(job_position: str) -> Dict:
    print(f"\n[INFO] Starting AI transformation analysis for: {job_position}")
    
    job_description = get_job_description(job_position)
    print(f"[DEBUG] Job Description: {json.dumps(job_description)[:500]}...")
    
    extracted_tasks = extract_tasks(job_description)
    print(f"[DEBUG] Extracted Tasks: {json.dumps(extracted_tasks)[:500]}...")
    
    automation_categorization = categorize_tasks_by_automation(extracted_tasks)
    automation_analysis = automation_categorization.get("automation_analysis", [])
    print(f"[DEBUG] Automation Categorization: {json.dumps(automation_categorization)[:500]}...")

    task_analysis_summary = []
    if isinstance(automation_analysis, list):
        for i, task_info in enumerate(automation_analysis):
            original_task = extracted_tasks[i] if i < len(extracted_tasks) else {}
            task_analysis_summary.append({
                "task_name": original_task.get('task_name', 'N/A'),
                "ai_involvement_percentage": task_info.get('ai_involvement_percentage', 'N/A'),
                "automation_category": task_info.get('automation_category', 'N/A').replace('_', ' ').title(),
                "explanation": task_info.get('explanation', '')
            })
            
    recommendations = generate_ai_transition_recommendations(job_description, automation_analysis)
    print(f"[DEBUG] Recommendations: {json.dumps(recommendations)[:500]}...")

    report_payload = {
        "job_position": job_position,
        "job_description": job_description,
        "extracted_tasks": extracted_tasks,
        "task_analysis_summary": task_analysis_summary,
        "future_skill_recommendations": recommendations.get("future_skill_recommendations", []),
        "sustainability_plan": recommendations.get("sustainability_plan", []),
        "opportunities_identification": recommendations.get("opportunities_identification", []),
    }
    
    html_report = generate_html_report(report_payload)

    return {
        "job_position": job_position,
        "job_description": job_description,
        "extracted_tasks": extracted_tasks,
        "automation_categorization": automation_categorization,
        "future_skill_recommendations": recommendations.get("future_skill_recommendations", []),
        "sustainability_plan": recommendations.get("sustainability_plan", []),
        "opportunities_identification": recommendations.get("opportunities_identification", []),
        "html_report": html_report,
        "status": "success"
    }

def validate_job_position(position: str) -> tuple[bool, str]:
    position = position.strip()
    if not position: return False, "Job position cannot be empty"
    if len(position) < 3: return False, "Job position must be at least 3 characters long"
    if len(position) > 100: return False, "Job position cannot exceed 100 characters"
    if any(char in '!@#$%^&*()+=[]{}|\\:;"<>?/' for char in position): return False, "Job position contains invalid special characters"
    if position.replace(" ", "").isnumeric(): return False, "Job position cannot be numeric only"
    if any(pattern in position.lower() for pattern in ['www.', 'http', '.com', '.net', '.org']): return False, "Job position cannot contain URLs"
    return True, ""

def get_valid_job_position() -> str:
    max_attempts = 3
    for i in range(max_attempts):
        position = input("\nEnter the job position to analyze: ").strip()
        is_valid, error_message = validate_job_position(position)
        if is_valid:
            return position
        print(f"\n[ERROR] {error_message}")
        if i < max_attempts - 1:
            print(f"Please try again. {max_attempts - 1 - i} attempts remaining.")
    raise ValueError("Maximum number of invalid input attempts reached.")

def parse_json_safely(json_str: str):
    if not isinstance(json_str, str):
        return json_str 
    try:
        match = re.search(r'```(?:json)?\s*(\{.*\}|\[.*\])\s*```', json_str, re.DOTALL)
        if match:
            json_str = match.group(1)
        return json.loads(json_str)
    except json.JSONDecodeError:
        try:
            return json.loads(repair_json(json_str))
        except (json.JSONDecodeError, ValueError):
            try:
                return dirtyjson.loads(json_str)
            except Exception as e:
                print(f"[ERROR] Final attempt to parse JSON failed: {e}")
                return {}

if __name__ == "__main__":    
    if len(sys.argv) > 1 and sys.argv[1] == "--api":
        print("\nStarting AI Job Transformation API Server")
        print("=" * 50)
        uvicorn.run(app, host="0.0.0.0", port=8000)
    else:
        print("\nAI Impact Analysis Tool")
        print("=" * 50)
        try:
            position = get_valid_job_position()
            print(f"\n[INFO] Analyzing position: {position}")
            transformation_results = analyze_job_for_ai_transformation(position)

            if transformation_results.get("status") == "error":
                print(f"\n[ERROR] Analysis failed: {transformation_results.get('message')}")
                exit(1)

            if transformation_results.get("html_report"):
                report_filename = f"AI_Impact_Report_{position.replace(' ', '_')}.html"
                with open(report_filename, "w", encoding="utf-8") as f:
                    f.write(transformation_results["html_report"])
                print(f"\n[SUCCESS] HTML report saved to: {report_filename}")

            print("\n--- Analysis Summary ---")
            print(f"Job Position: {transformation_results['job_position']}")
            if transformation_results.get('job_description'):
                 print("\nJob Description Summary:")
                 print(json.dumps(transformation_results['job_description'].get('Description', 'N/A'), indent=2))
            if transformation_results.get('automation_categorization', {}).get('automation_summary'):
                print("\nAutomation Summary:")
                print(json.dumps(transformation_results['automation_categorization']['automation_summary'], indent=2))
            if transformation_results.get('future_skill_recommendations'):
                print("\nFuture Skill Recommendations:")
                print(json.dumps(transformation_results['future_skill_recommendations'], indent=2))
            if transformation_results.get('opportunities_identification'):
                print("\nNew Opportunities:")
                print(json.dumps(transformation_results['opportunities_identification'], indent=2))

        except ValueError as e:
            print(f"\n[ERROR] {str(e)}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"\n[ERROR] An unexpected error occurred: {str(e)}")
        finally:
            print("\n" + "=" * 50)
