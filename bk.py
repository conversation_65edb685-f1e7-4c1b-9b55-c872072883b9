import datetime
import re
import json
import os
from typing import List, Dict
from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager

# Configuration
config_list_bedrock = [
    {
        "api_type": "bedrock",
        "model": "anthropic.claude-3-haiku-20240307-v1:0",
        "aws_region": "us-east-1",
        "aws_access_key": "********************",
        "aws_secret_key": "ghYKGewFG7lnQ+uhBxSGB48r+x2CCxwnpHLrpDbN",
        "temperature": 0.4,
    }
]
llm_config = {
    "config_list": config_list_bedrock,
}

# Consolidated Agents
agent_job_analyzer = AssistantAgent(
    name="Job_Analyzer",
    system_message="""You are an expert in job analysis and description generation.
    Your role is to:
    1. Generate comprehensive job descriptions
    2. Validate and correct job descriptions
    3. Extract key insights from job requirements
    4. Ensure content quality and accuracy""",
    llm_config=llm_config
)

agent_ai_impact_analyzer = AssistantAgent(
    name="AI_Impact_Analyzer",
    system_message="""You are an expert in AI transformation and impact analysis.
    Your role is to:
    1. Analyze AI impact on job roles
    2. Evaluate task effort and time management
    3. Calculate productivity improvements
    4. Assess implementation complexity""",
    llm_config=llm_config
)

agent_content_validator = AssistantAgent(
    name="Content_Validator",
    system_message="""You are an expert in content validation and quality assurance.
    Your role is to:
    1. Validate content accuracy and completeness
    2. Ensure proper HTML formatting
    3. Check for consistency and clarity
    4. Verify technical feasibility""",
    llm_config=llm_config
)

agent_job_describer = AssistantAgent(
    name="Job_Describer",
    system_message="""You are an expert in job analysis and description.
    Your role is to:
    1. Provide concise, clear descriptions of job positions
    2. Highlight key responsibilities and requirements
    3. Explain the role's importance in the organization
    4. Keep descriptions brief and informative
    5. Focus on the most critical aspects of the role""",
    llm_config=llm_config
)
agent_job_corrector = AssistantAgent(
    name="Job_Description_Corrector",
    system_message="""You are a job description corrector.
            Your task is to:
            1. Correct grammar, spelling, or structural errors in the job description provided.
            2. Ensure clarity, professionalism, and conciseness.
            3. Keep the original intent and content intact.
            4. Do not add new content or commentary.
            5. Return only the corrected job description without any additional message, explanation, or prefix.""",
    llm_config=llm_config
)

agent_task_automation_categorizer = AssistantAgent(
    name="Task_Automation_Categorizer",
    system_message="""
    You are an expert in AI automation analysis. Your role is to categorize job tasks based on their automation potential.
    
    Your task is to:
    1. Analyze each job task and determine if it can be:
       a) FULLY AUTOMATED by AI (90-100% automation)
       b) PARTIALLY AUTOMATED/HYBRID by AI (30-89% automation)
       c) NOT AUTOMATABLE (0-29% automation)
    2. Provide a detailed explanation for each categorization.
    3. Estimate the percentage of AI involvement for each task.

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.
    The output should follow this exact structure:
    
    ```json
    {
        "automation_analysis": [
            {
                "task": "Task description",
                "automation_category": "FULLY_AUTOMATED",
                "ai_involvement_percentage": 100,
                "explanation": "Detailed explanation of why this task can be fully automated."
            }
        ]
    }
    ```
    """,
    llm_config=llm_config
)

recommendation_agent = AssistantAgent(
    name="Recommendation_Agent",
    system_message="""
    You are an expert in generating strategic recommendations for AI transformation. Your role is to provide actionable plans for future skills, workforce sustainability, and new opportunities.

    Your response MUST be a JSON object containing three keys: `future_skill_recommendations`, `sustainability_plan`, and `opportunities_identification`.

    - **`future_skill_recommendations`**: (List of objects) Detail the skills needed for the AI-enhanced role. Each object should have `skill_area`, `specific_skills`, and `relevance_to_ai_role`.
    - **`sustainability_plan`**: (List of objects) Based on the definition of workforce sustainability (Workforce sustainability in the AI era involves balancing AI's potential to enhance productivity with the need to maintain decent jobs and adapt to technological changes. AI can automate routine tasks, potentially displacing some roles (e.g., 25-40% of global jobs exposed), while creating new opportunities in areas like AI ethics, system training, and sustainability solutions. To ensure sustainability, strategies include upskilling workers for technical, human, and conceptual skills, fostering human-AI collaboration, and implementing policies that promote reskilling, equitable job transitions, and reduced environmental impact from AI infrastructure. Challenges include job polarization, inequality, and AI's energy demands, which require careful management to align with sustainable growth goals.), provide initiatives. Each object should have `area`, `initiative`, and `impact_metric`.
    - **`opportunities_identification`**: (List of objects) Identify new business opportunities created by AI adoption. Each object should have `opportunity`, `description`, and `potential_value_proposition`.
    
    Example structure:
    ```json
    {
        "future_skill_recommendations": [
            {
                "skill_area": "Data Literacy and Analysis",
                "specific_skills": ["Understanding AI model outputs", "Data-driven decision making"],
                "relevance_to_ai_role": "Essential for interpreting AI insights."
            }
        ],
        "sustainability_plan": [
            {
                "area": "Continuous Learning and Upskilling",
                "initiative": "Develop personalized learning paths for employees to acquire critical AI-related skills.",
                "impact_metric": "Increased employee retention and internal mobility."
                "ai_tools_or_methods": ["AI-powered learning platforms", "Career pathing tools"]
                "human_role": "Curate and facilitate learning paths"
                "timeline": "0-3 months"
            }
        ],
        "opportunities_identification": [
            {
                "opportunity": "Personalized Customer Experience at Scale",
                "description": "Leverage AI to analyze customer data and provide highly personalized recommendations.",
                "potential_value_proposition": "Increased customer retention by 15%."
            }
        ]
    }
    ```
    """,
    llm_config=llm_config
)


user_proxy = UserProxyAgent(
    name="User_Proxy",
    system_message="Execute tasks and coordinate between agents.",
    human_input_mode="NEVER",
    code_execution_config={"use_docker": False},
    max_consecutive_auto_reply=3,
    default_auto_reply="Please continue. If finished, reply 'TERMINATE'.",
    llm_config=llm_config
)


def get_job_description(job_position: str) -> Dict:
    print(f"\n[INFO] Getting job description for: {job_position}")

    description_prompt = f"""
    Provide a comprehensive description of the job position: {job_position}
    Include:
    1. Overview of the role
    2. Key responsibilities and duties
    3. Required skills and qualifications
    4. Typical work environment
    5. Career progression opportunities
    """

    group_chat = GroupChat(
        agents=[agent_job_describer, agent_job_corrector],
        messages=[],
        max_round=3
    )
    manager = GroupChatManager(groupchat=group_chat, llm_config=llm_config)

    user_proxy.initiate_chat(manager, message=description_prompt, silent=True)
    all_messages = manager.groupchat.messages

    job_description = all_messages[-1]["content"]

    return {"content": job_description}


def analyze_ai_impact(job_position: str) -> Dict:
    analysis_prompt = f"""
    Analyze AI impact on {job_position}:
    1. Current key responsibilities
    2. Potential AI applications
    3. Impact on workflow
    4. Required skill adaptations
    5. Time and effort analysis
    6. Implementation complexity
    """

    analysis_group = GroupChat(
        agents=[agent_ai_impact_analyzer, agent_content_validator],
        messages=[],
        max_round=2,
        speaker_selection_method="round_robin"
    )

    manager = GroupChatManager(groupchat=analysis_group, llm_config=llm_config)
    initial_analysis = user_proxy.initiate_chat(
        manager,
        message=analysis_prompt
    )
    all_messages = manager.groupchat.messages

    analyse_message = all_messages[-1]["content"]

    return {"content": analyse_message}


def analyze_ai_task_transformation(job_position: str) -> dict:
    print(f"\n[INFO] Analyzing AI task transformation for: {job_position}")

    # Get job description
    job_description = get_job_description(job_position)

    # Get initial analysis
    base_analysis = analyze_ai_impact(job_position)

    # Create analysis groups for different aspects
    task_analysis_group = GroupChat(
        agents=[
            agent_ai_impact_analyzer,
            agent_content_validator
        ],
        messages=[],
        max_round=3,
        speaker_selection_method="round_robin"
    )

    # Strict prompt for transformed tasks
    transformed_tasks_prompt = f"""
    For the role of {job_position}, list ONLY the tasks that will be transformed by AI. STRICT INSTRUCTIONS:
    - DO NOT include any conversational, polite, or generic language.
    - DO NOT mention yourself or your process.
    - List each transformed task as a bullet point.
    - For each, briefly state the change and the expected impact.
    - Be concise and factual.
    """

    task_manager = GroupChatManager(groupchat=task_analysis_group, llm_config=llm_config)
    transformed_analysis = user_proxy.initiate_chat(
        task_manager,
        message=transformed_tasks_prompt
    )
    all_messages = task_manager.groupchat.messages

    transformed_analysis_message = all_messages[-1]["content"]
    new_tasks_prompt = f"""
    For the role of {job_position}, list ONLY the new tasks enabled by AI. STRICT INSTRUCTIONS:
    - DO NOT include any conversational, polite, or generic language.
    - DO NOT mention yourself or your process.
    - List each new AI-enabled task as a bullet point.
    - For each, briefly state the purpose and required skills.
    - Be concise and factual.
    """

    new_tasks_analysis = user_proxy.initiate_chat(
        task_manager,
        message=new_tasks_prompt
    )
    all_messages = task_manager.groupchat.messages

    new_tasks_analysis_message = all_messages[-1]["content"]
    # Strict prompt for preserved tasks
    preserved_tasks_prompt = f"""
    For the role of {job_position}, list ONLY the tasks that should remain human-driven. STRICT INSTRUCTIONS:
    - DO NOT include any conversational, polite, or generic language.
    - DO NOT mention yourself or your process.
    - List each preserved task as a bullet point.
    - For each, briefly state why it should remain human-driven.
    - Be concise and factual.
    """

    preserved_analysis = user_proxy.initiate_chat(
        task_manager,
        message=preserved_tasks_prompt
    )
    all_messages = task_manager.groupchat.messages

    preserved_analysis_message = all_messages[-1]["content"]
    # Strict prompt for executive summary
    summary_prompt = f"""
    You are to generate an executive summary for the AI transformation of the role: {job_position}.
    STRICT INSTRUCTIONS:
    - DO NOT include any greetings, thanks, or conversational language.
    - DO NOT mention yourself, your process, or your willingness to help.
    - ONLY include factual, analytical, and actionable content.
    - Structure the summary as follows:
      1. Key findings from transformed tasks (concise bullet points)
      2. New AI-enabled opportunities (concise bullet points)
      3. Preserved human elements (concise bullet points)
      4. Overall impact and recommendations (concise bullet points)
    - The output should be a list of bullet points or a short, structured paragraph for each section.
    - Do not include any sentences that are not directly related to the analysis.
    Here is the analysis data:
    - Transformed tasks: {transformed_analysis.summary}
    - New AI-enabled tasks: {new_tasks_analysis.summary}
    - Preserved human elements: {preserved_analysis.summary}
    """

    final_summary = agent_content_validator.generate_reply(
        messages=[{"role": "user", "content": summary_prompt}],
        sender=user_proxy
    )

    # Add time-effort gap analysis (with strict prompt)
    gap_analysis_prompt = f"""
    For the role of {job_position}, analyze the time-effort gap between current and AI-enhanced workflows. STRICT INSTRUCTIONS:
    - DO NOT include any conversational, polite, or generic language.
    - DO NOT mention yourself or your process.
    - Provide a bullet-point or table comparison for each task.
    - For each, show current time, AI-enhanced time, time saved, and a brief reason for the difference.
    - Be concise and factual.
    Data:
    - Transformed tasks: {transformed_analysis.summary}
    - New AI-enabled tasks: {new_tasks_analysis.summary}
    - Preserved human tasks: {preserved_analysis.summary}
    """

    time_effort_group = GroupChat(
        agents=[
            agent_ai_impact_analyzer,
            agent_content_validator
        ],
        messages=[],
        max_round=3,
        speaker_selection_method="round_robin"
    )
    gap_manager = GroupChatManager(groupchat=time_effort_group, llm_config=llm_config)
    gap_analysis_result = user_proxy.initiate_chat(
        gap_manager,
        message=gap_analysis_prompt
    )
    all_messages = gap_manager.groupchat.messages

    gap_analysis_message = all_messages[-1]["content"]
    gap_analysis = {
        "content": gap_analysis_result.summary,
        "metrics": {
            "time_saved_weekly": "Estimated in analysis",
            "productivity_gain": "Estimated in analysis",
            "implementation_complexity": "Assessed in analysis"
        },
        "task_time_comparison": "Detailed in analysis"
    }

    return {
        "job_description": job_description,
        "original_analysis": base_analysis,
        "transformed_tasks": transformed_analysis_message,
        "new_tasks": new_tasks_analysis_message,
        "preserved_tasks": preserved_analysis_message,
        "executive_summary": final_summary,
        "time_effort_analysis": gap_analysis_message,
        "timestamp": datetime.datetime.now().isoformat()
    }


def format_as_qa(question: str, content: str, subtopics: list) -> str:
    # Create a copy of the content for processing
    processed_content = content

    for subtopic in subtopics:
        pattern = re.compile(f"({re.escape(subtopic)}:?)", re.IGNORECASE)
        processed_content = pattern.sub(r'<span class="highlight-title">\1</span>', processed_content)

    for subtopic in subtopics:
        processed_content = processed_content.replace(subtopic + ":", "")
        processed_content = processed_content.replace(subtopic, "")

    processed_content = re.sub(r'\d+\.\s+', '', processed_content)

    processed_content = processed_content.replace('- ', '')
    processed_content = processed_content.replace('• ', '')

    # Format as Q&A
    formatted = f"<div class='qa-container'>\n"
    formatted += f"<div class='question'><strong>{question}</strong></div>\n"
    formatted += f"<div class='answer'>{processed_content}</div>\n"
    formatted += f"</div>\n"

    return formatted


def format_task_data_for_chart(analysis_result: dict) -> dict:
    """
    Extract and format task data for chart generation.

    Args:
        analysis_result (dict): The complete analysis result

    Returns:
        dict: Formatted task data for charting with the following structure:
        {
            "tasks": [
                {
                    "name": "Task Name",
                    "current_time": float,  # hours per week
                    "ai_time": float,      # hours per week
                    "time_saved": float,    # hours per week
                    "efficiency_gain": float,  # percentage
                    "category": str,        # e.g., "transformed", "new", "preserved"
                    "impact_level": str,    # e.g., "high", "medium", "low"
                    "ai_tools": list,       # list of AI tools recommended
                    "human_oversight": str  # level of human oversight needed
                },
                ...
            ],
            "summary": {
                "total_current_time": float,
                "total_ai_time": float,
                "total_time_saved": float,
                "overall_efficiency_gain": float,
                "task_distribution": {
                    "transformed": int,
                    "new": int,
                    "preserved": int
                }
            }
        }
    """
    task_data = {
        "tasks": [],
        "summary": {
            "total_current_time": 0,
            "total_ai_time": 0,
            "total_time_saved": 0,
            "overall_efficiency_gain": 0,
            "task_distribution": {
                "transformed": 0,
                "new": 0,
                "preserved": 0
            }
        }
    }

    # Extract task data from time-effort analysis
    if 'time_effort_analysis' in analysis_result:
        content = analysis_result['time_effort_analysis']

        # Look for task patterns in the content
        task_pattern = r'- Task name: ([^\n]+)\n- Current time: ([^\n]+)\n- AI-enhanced time: ([^\n]+)\n- Category: ([^\n]+)\n- Impact level: ([^\n]+)\n- AI tools: ([^\n]+)\n- Human oversight: ([^\n]+)'
        matches = re.findall(task_pattern, content, re.MULTILINE)

        for task_name, current_time, ai_time, category, impact_level, ai_tools, human_oversight in matches:
            try:
                # Extract numeric values from time strings
                current_hours = float(re.search(r'(\d+\.?\d*)', current_time).group(1))
                ai_hours = float(re.search(r'(\d+\.?\d*)', ai_time).group(1))
                time_saved = current_hours - ai_hours
                efficiency_gain = ((current_hours - ai_hours) / current_hours) * 100 if current_hours > 0 else 0

                # Update summary statistics
                task_data["summary"]["total_current_time"] += current_hours
                task_data["summary"]["total_ai_time"] += ai_hours
                task_data["summary"]["total_time_saved"] += time_saved
                task_data["summary"]["task_distribution"][category.lower()] += 1

                # Add task to list
                task_data["tasks"].append({
                    "name": task_name,
                    "current_time": current_hours,
                    "ai_time": ai_hours,
                    "time_saved": time_saved,
                    "efficiency_gain": efficiency_gain,
                    "category": category.lower(),
                    "impact_level": impact_level.lower(),
                    "ai_tools": [tool.strip() for tool in ai_tools.split(',')],
                    "human_oversight": human_oversight.lower()
                })
            except (ValueError, AttributeError):
                continue

        # Calculate overall efficiency gain
        if task_data["summary"]["total_current_time"] > 0:
            task_data["summary"]["overall_efficiency_gain"] = (
                                                                      (task_data["summary"]["total_current_time"] -
                                                                       task_data["summary"]["total_ai_time"]) /
                                                                      task_data["summary"]["total_current_time"]
                                                              ) * 100

    return task_data


def generate_comparison_chart(task_data: dict) -> dict:
    """
    Generate chart data and configuration for comparing traditional vs AI-enhanced tasks.
    Args:
        task_data (dict): Dictionary containing task comparison data.
    Returns:
        dict: Chart configuration for Chart.js
    """
    # Fallback: build the chart config directly
    return {
        "type": "bar",
        "data": {
            "labels": [task["name"] for task in task_data["tasks"]],
            "datasets": [
                {
                    "label": "Traditional Time",
                    "data": [task["current_time"] for task in task_data["tasks"]],
                    "backgroundColor": "#4CAF50"
                },
                {
                    "label": "AI-Enhanced Time",
                    "data": [task["ai_time"] for task in task_data["tasks"]],
                    "backgroundColor": "#2196F3"
                }
            ]
        },
        "options": {
            "responsive": True,
            "plugins": {
                "title": {
                    "display": True,
                    "text": "Task Time Comparison: Traditional vs AI-Enhanced"
                },
                "tooltip": {
                    "callbacks": {
                        "label": "function(context) { return context.dataset.label + ': ' + context.parsed.y + ' hours/week'; }"
                    }
                }
            },
            "scales": {
                "y": {
                    "beginAtZero": True,
                    "title": {
                        "display": True,
                        "text": "Time (hours/week)"
                    }
                }
            }
        }
    }


def split_to_bullets(text):
    # Split text into sentences and wrap each in <li>
    import re
    # Remove HTML tags if present
    clean_text = re.sub(r'<.*?>', '', text)
    # Split by sentence-ending punctuation
    sentences = re.split(r'(?<=[.!?])\s+', clean_text.strip())
    bullets = [f'<li>{s.strip()}</li>' for s in sentences if s.strip()]
    return '<ul>' + '\n'.join(bullets) + '</ul>' if bullets else ''


def generate_transformation_report(analysis_result: dict, role: str) -> str:
    print("\n[INFO] Generating transformation report...")

    # Format all sections
    sections = {
        'job_description': analysis_result['job_description'],
        'transformed_tasks': analysis_result['transformed_tasks'],
        'new_tasks': analysis_result['new_tasks'],
        'preserved_tasks': analysis_result['preserved_tasks'],
        'executive_summary': analysis_result['executive_summary']["content"],
        'time_effort_analysis': analysis_result['time_effort_analysis']
    }

    # Extract time metrics for dashboard
    productivity_gain = "N/A"
    time_saved_weekly = "N/A"
    implementation_complexity = "N/A"

    if 'time_effort_analysis' in analysis_result and isinstance(analysis_result['time_effort_analysis'], dict):
        metrics = analysis_result['time_effort_analysis']
        time_saved_weekly = metrics.get('time_saved_weekly', 'N/A')
        productivity_gain = metrics.get('productivity_gain', 'N/A')
        implementation_complexity = metrics.get('implementation_complexity', 'N/A')

    # Generate chart data
    task_data = format_task_data_for_chart(analysis_result)
    chart_config = generate_comparison_chart(task_data)

    # Organize each section as bullet points or sentences
    exec_summary_html = split_to_bullets(str(sections['executive_summary']))
    time_effort_html = split_to_bullets(
        str(sections.get('time_effort_analysis', {})))
    job_desc_html = split_to_bullets(str(sections['job_description']['content']))
    transformed_html = split_to_bullets(str(sections['transformed_tasks']))
    new_tasks_html = split_to_bullets(str(sections['new_tasks']))
    preserved_html = split_to_bullets(str(sections['preserved_tasks']))

    # Create the HTML template with improved styling
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI Task Transformation Analysis - {role}</title>
        <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>
        <style>
            :root {{
                --primary-color: #2c3e50;
                --secondary-color: #3498db;
                --accent-color: #e74c3c;
                --background-color: #f8f9fa;
                --card-background: #ffffff;
                --text-color: #2c3e50;
                --border-color: #e0e0e0;
            }}
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: var(--text-color);
                background-color: var(--background-color);
                margin: 0;
                padding: 0;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }}
            header {{
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                color: white;
                padding: 2rem 0;
                margin-bottom: 2rem;
                text-align: center;
            }}
            .metrics-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 2rem;
            }}
            .metric-card {{
                background: var(--card-background);
                padding: 1.5rem;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                text-align: center;
                transition: transform 0.3s ease;
            }}
            .metric-card:hover {{
                transform: translateY(-5px);
            }}
            .metric-value {{
                font-size: 2rem;
                font-weight: bold;
                color: var(--secondary-color);
                margin: 0.5rem 0;
            }}
            .metric-label {{
                font-size: 1rem;
                color: var(--text-color);
                opacity: 0.8;
            }}
            .section {{
                background: var(--card-background);
                border-radius: 8px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .section-title {{
                color: var(--primary-color);
                margin-bottom: 1.5rem;
                padding-bottom: 0.5rem;
                border-bottom: 2px solid var(--border-color);
            }}
            .section-content {{
                color: var(--text-color);
                line-height: 1.8;
            }}
            .chart-container {{
                position: relative;
                height: 400px;
                margin: 2rem 0;
                background: var(--card-background);
                padding: 1rem;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .analysis-block {{
                margin-bottom: 1.5rem;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 4px;
            }}
            .analysis-title {{
                font-weight: bold;
                color: var(--secondary-color);
                margin-bottom: 0.5rem;
            }}
            .analysis-content {{
                color: var(--text-color);
                line-height: 1.6;
            }}
            @media (max-width: 768px) {{
                .metrics-grid {{
                    grid-template-columns: 1fr;
                }}
                .section {{
                    padding: 1rem;
                }}
            }}
        </style>
    </head>
    <body>
        <header>
            <div class="container">
                <h1>AI Task Transformation Analysis</h1>
                <p>Position: {role}</p>
            </div>
        </header>
        <div class="container">
            <!-- Key Metrics -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">Time Saved Weekly</div>
                    <div class="metric-value">{time_saved_weekly}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Productivity Gain</div>
                    <div class="metric-value">{productivity_gain}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Implementation Complexity</div>
                    <div class="metric-value">{implementation_complexity}</div>
                </div>
            </div>
            <!-- Executive Summary -->
            <div class="section">
                <h2 class="section-title">Executive Summary</h2>
                <div class="section-content">
                    {exec_summary_html}
                </div>
            </div>
            <!-- Job Description -->
            <div class="section">
                <h2 class="section-title">Job Description</h2>
                <div class="section-content">
                    {job_desc_html}
                </div>
            </div>
            <!-- Task Analysis -->
            <div class="section">
                <h2 class="section-title">Task Transformation Analysis</h2>
                <div class="analysis-block">
                    <div class="analysis-title">Transformed Tasks</div>
                    <div class="analysis-content">
                        {transformed_html}
                    </div>
                </div>
                <div class="analysis-block">
                    <div class="analysis-title">New AI-Enabled Tasks</div>
                    <div class="analysis-content">
                        {new_tasks_html}
                    </div>
                </div>
                <div class="analysis-block">
                    <div class="analysis-title">Preserved Human Tasks</div>
                    <div class="analysis-content">
                        {preserved_html}
                    </div>
                </div>
            </div>
            <!-- Time-Effort Analysis -->
            <div class="section">
                <h2 class="section-title">Time-Effort Analysis</h2>
                <div class="section-content">
                    {time_effort_html}
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    return html_template


def validate_job_position(position: str) -> tuple[bool, str]:
    position = position.strip()

    if not position:
        return False, "Job position cannot be empty"
    if len(position) < 3:
        return False, "Job position must be at least 3 characters long"
    if len(position) > 100:
        return False, "Job position cannot exceed 100 characters"

    invalid_chars = set('!@#$%^&*()+=[]{}|\\:;"<>?/')
    if any(char in invalid_chars for char in position):
        return False, "Job position contains invalid special characters"
    if position.replace(" ", "").isnumeric():
        return False, "Job position cannot be numeric only"

    spam_patterns = ['www.', 'http', '.com', '.net', '.org']
    if any(pattern in position.lower() for pattern in spam_patterns):
        return False, "Job position cannot contain URLs or web addresses"

    return True, ""


def get_valid_job_position() -> str:
    max_attempts = 2
    attempts = 0

    while attempts < max_attempts:
        position = input("\nEnter the job position to analyze: ").strip()
        is_valid, error_message = validate_job_position(position)

        if is_valid:
            description_prompt = f"""
            Provide a brief description of the job position: {position}
            Include:
            1. A one-sentence overview
            2. 2-3 key responsibilities
            3. Main required skills
            4. Typical work environment
            Keep the response concise and informative.
            """
            group_chat = GroupChat(
                agents=[agent_job_describer,agent_job_corrector],
                messages=[],
                max_round=3
            )
            manager = GroupChatManager(groupchat=group_chat, llm_config=llm_config)

            user_proxy.initiate_chat(manager, message=description_prompt, silent=True)
            all_messages = manager.groupchat.messages


            job_description = all_messages[-1]["content"]
            print("\n[INFO] Job Position Overview:")
            print("=" * 50)
            print(job_description)
            print("=" * 50)

            return position

        attempts += 1
        remaining = max_attempts - attempts
        print(f"\n[ERROR] {error_message}")
        if remaining > 0:
            print(f"Please try again. {remaining} attempts remaining.")

    raise ValueError("Maximum number of invalid input attempts reached. Please restart the program.")


if __name__ == "__main__":
    print("=" * 50)

    try:
        role = get_valid_job_position()
        print(f"\n[INFO] Analyzing position: {role}")

        # Get transformation analysis
        transformation_results = analyze_ai_task_transformation(role)

        # Display results
        print("\nJob Description:")
        print(transformation_results["job_description"]["content"])

        print("\nTransformed Tasks Analysis:")
        print(transformation_results["transformed_tasks"])

        print("\nNew AI-Enabled Tasks:")
        print(transformation_results["new_tasks"])

        print("\nPreserved Human Tasks:")
        print(transformation_results["preserved_tasks"])

        print("\nExecutive Summary:")
        print(transformation_results["executive_summary"]["content"])

        print(f"\n[SUCCESS] Report generated successfully")

    except ValueError as e:
        print(f"\n[ERROR] {str(e)}")
    except Exception as e:
        print(f"\n[ERROR] An unexpected error occurred: {str(e)}")
    finally:
        print("\n" + "=" * 50)
