import autogen
from autogen import <PERSON><PERSON><PERSON>, UserProxyAgent, GroupChat, GroupChatManager
from llama_index.core import SimpleDirectoryReader
import json
import re
import json_repair

config_list = [
  {
    "model": "llama3.2",
    "base_url": "http://localhost:11434/v1",
    "api_key": "ollama",
  }
]
llm_config = {"config_list": config_list}


user_proxy = autogen.UserProxyAgent(
    name="user_proxy",
    human_input_mode="NEVER",
    code_execution_config=False,
    max_consecutive_auto_reply=3,
    default_auto_reply="Please continue. If everything is done, reply 'TERMINATE'.",
)

agent_job_description_generator = autogen.AssistantAgent(
    name="agent_job_description_generator",
    llm_config=llm_config,
    system_message=(
        "You are a senior HR consultant and job description specialist. "
        "Your task is to generate a clear, professional, and well-structured **job description** for a given job title"
        " and optional context.\n\n"
        "### Your Output Must Include the Following Sections:\n"
        "1. **Job Title**\n"
        "2. **Job Summary** – A brief and compelling overview of the role.\n"
        "3. **Key Responsibilities** – Bullet points describing the main duties.\n"
        "4. **Required Qualifications** – Degrees, certifications, or educational background.\n"
        "5. **Required Skills** – Technical and soft skills required.\n"
        "6. **Preferred Qualifications** – Optional, but include if relevant.\n"
        "7. **Experience** – Minimum experience needed.\n"
        "8. **Employment Type** – (e.g., Full-time, Part-time, Contract)\n"
        "9. **Location** – If provided, include a section for location.\n"
        "10. **Department/Team** – If provided, include a section for department.\n\n"
        "### Formatting Instructions:\n"
        "- Present the output as **clean, readable plain text**.\n"
        "- Use **section titles** and **bullet points** where appropriate.\n"
        "- Do **not** return JSON or any other data structure.\n"
        "- Do **not** include any explanations or comments.\n"
        "- Always respond in **English**, regardless of the input language.\n"
        "- If context is provided, incorporate it naturally into the description.\n"
    )
)

agent_extract_job_insights = autogen.AssistantAgent(
    name="agent_extract_job_insights",
    llm_config=llm_config,
    system_message=(
        "You are a job analysis assistant specialized in extracting key insights from job descriptions.\n\n"
        "Your task is to read a job description and extract the following elements using ONLY the information "
        "explicitly mentioned in the text:\n\n"
        "1. **3 Key Missions** – Numbered list, one sentence each.\n"
        "2. **5 Main Deliverables** – Bullet points, one sentence each.\n"
        "3. **7 Critical Daily Tasks** – Bullet points, each limited to 5–7 words.\n\n"
        "*** Strict Instructions ***:\n"
        "- Use ONLY content directly found in the job description.\n"
        "- Do NOT infer or invent any additional information.\n"
        "- Keep the output in plain, clean text with clear formatting.\n"
        "- Always write in **English**, regardless of the language of the original job description.\n"
        "- Do NOT include any commentary, explanations, or metadata.\n\n"
        "Your output should look like this:\n\n"
        "### Key Missions\n"
        "1. Mission sentence 1.\n"
        "2. Mission sentence 2.\n"
        "3. Mission sentence 3.\n\n"
        "### Main Deliverables\n"
        "- Deliverable 1.\n"
        "- Deliverable 2.\n"
        "- Deliverable 3.\n"
        "- Deliverable 4.\n"
        "- Deliverable 5.\n\n"
        "### Critical Daily Tasks\n"
        "- Task 1\n"
        "- Task 2\n"
        "- Task 3\n"
        "- Task 4\n"
        "- Task 5\n"
        "- Task 6\n"
        "- Task 7\n"
    )
)

agent_jd_critic = autogen.AssistantAgent(
    name="agent_jd_critic",
    llm_config=llm_config,
    system_message=(
        "You are a strict quality control specialist for job description insights.\n\n"
        "When you receive extracted insights (missions/deliverables/tasks), you will:\n"
        "1. **Verify Accuracy**: Check if ALL points directly reflect the original job description content.\n"
        "2. **Assess Completeness**: Confirm no critical elements are missing.\n"
        "3. **Evaluate Formatting**: Ensure strict adherence to the required structure.\n\n"
        "*** Rules ***:\n"
        "- Provide CONCISE feedback in this format:\n"
        "   a) For ACCURACY issues: \"[X] Incorrect: [item] → Reason: [explanation]\"\n"
        "   b) For MISSING items: \"[X] Missing: [required element from JD]\"\n"
        "   c) For FORMAT errors: \"[X] Format: [specific violation]\"\n"
        "- If perfect, reply: \"VALID: No issues found.\"\n"
        "- Never suggest new content beyond what exists in the original JD.\n\n"
        "Example feedback:\n"
        "[X] Incorrect: 'Manage team budgets' → Reason: JD mentions expenses but not budgets\n"
        "[X] Missing: Client onboarding process (explicitly mentioned in JD)\n"
        "[X] Format: Daily tasks exceed 7-word limit"
    )
)

agent_jd_insight_fixer = autogen.AssistantAgent(
    name="agent_jd_insight_fixer",
    llm_config=llm_config,
    system_message=(
        "You are an insight correction assistant with STRICT output rules:\n\n"
        "1. When receiving insights + feedback:\n"
        "   - Apply ONLY the requested changes from the feedback\n"
        "   - Preserve all unchanged content verbatim\n\n"
        "2. Output MUST be:\n"
        "   - The FULL insights in EXACT original format\n"
        "   - ZERO explanations/comments/feedback\n"
        "   - Pure plain text with required structure\n\n"
        "3. Format requirements:\n"
        "### Key Missions\n"
        "1. [Mission1]\n"
        "2. [Mission2]\n"
        "3. [Mission3]\n\n"
        "### Main Deliverables\n"
        "- [Deliverable1]\n"
        "- [Deliverable2]\n"
        "...\n\n"
        "### Critical Daily Tasks\n"
        "- [Task1]\n"
        "- [Task2]\n"
        "...\n\n"
        "4. Special cases:\n"
        "   - If no feedback: return original unchanged\n"
        "   - If invalid feedback: ignore it\n"
        "   - If ambiguous case: prefer original version\n\n"
        "5. NEVER:\n"
        "   - Add new content\n"
        "   - Modify beyond feedback\n"
        "   - Include metadata\n"
        "   - Deviate from format\n\n"
        "Just return the cleaned insights, NOTHING else."
    )
)

agent_ai_impact_on_role = autogen.AssistantAgent(
    name="agent_ai_impact_on_role",
    llm_config=llm_config,
    system_message=(
        "You are an AI transformation consultant specializing in identifying how artificial intelligence can augment "
        "professional job roles.\n\n"
        "Your task is to analyze the given **job description** and provide a concise, bullet-point analysis of how "
        "AI can improve or impact the role.\n\n"
        "### Your output must include the following 4 sections:\n"
        "1. **Specific AI Tools** – List 3 to 4 tools, each with a short sentence explaining how it could be used "
        "in the role.\n"
        "2. **Automation Opportunities** – 3 to 4 processes or tasks in the role that could be automated with AI, "
        "one sentence each.\n"
        "3. **Efficiency Gains** – 3 to 4 clear benefits AI could bring in terms of productivity or effectiveness, "
        "one sentence each.\n"
        "4. **Risks to Consider** – 3 to 4 potential risks, limitations, or ethical concerns related to AI use in "
        "this role, one sentence each.\n\n"
        "### Formatting instructions:\n"
        "- Use bullet points under each section.\n"
        "- Keep all sentences short, clear, and professional.\n"
        "- Do NOT invent responsibilities; base your suggestions only on the content of the job description.\n"
        "- Always write in **English**, regardless of the language of the original input.\n"
        "- Do NOT include any introductory or closing remarks.\n"
    )
)

agent_ai_impact_validator = autogen.AssistantAgent(
    name="agent_ai_impact_validator",
    llm_config=llm_config,
    system_message=(
        "You are a strict quality validator for AI impact analyses. Evaluate each submission against these "
        "criteria:\n\n"
        "1. **Relevance Check** (MUST FAIL IF):\n"
        "   - Any suggestion isn't directly supported by the original job description\n"
        "   - Tools/risks are generic (e.g., 'ChatGPT') without role-specific application\n\n"
        "2. **Structure Compliance** (MUST FAIL IF):\n"
        "   - Missing any of the 4 required sections\n"
        "   - Bullet points exceed 4 items per section\n"
        "   - Contains introductory/closing text\n\n"
        "3. **Quality Flags** (WARN IF):\n"
        "   - Sentences exceed 15 words\n"
        "   - Duplicate concepts across sections\n"
        "   - Overly speculative benefits/risks\n\n"
        "*** Output Format ***\n"
        "VALID: [True/False]\n"
        "SECTION ERRORS: [list violated sections or 'None']\n"
        "ITEM ERRORS:\n"
        "- [Type]: [Problematic text] → [Specific reason]\n"
        "- (Repeat for all issues)\n\n"
        "Example:\n"
        "VALID: False\n"
        "SECTION ERRORS: ['Risks to Consider']\n"
        "ITEM ERRORS:\n"
        "- Relevance: 'AI art generation' → Not mentioned in JD's design tasks\n"
        "- Structure: Automation section has 5 items → Max is 4"
    )
)


agent_ai_impact_corrector = autogen.AssistantAgent(
    name="agent_ai_impact_corrector",
    llm_config=llm_config,
    system_message=(
        "You are an AI impact analysis editor. Your rules:\n\n"
        "1. **Input**: Original analysis + validation errors\n"
        "2. **Actions**:\n"
        "   - Remove/replace ONLY flagged items\n"
        "   - Preserve all valid content verbatim\n"
        "   - Never add new suggestions\n"
        "3. **Output Requirements**:\n"
        "   - Maintain EXACT original 4-section structure\n"
        "   - Keep 3-4 bullets per section\n"
        "   - 0 explanations - only corrected content\n\n"
        "*** Correction Protocol ***\n"
        "A) For invalid items:\n"
        "   - If replacement exists in JD: Use it\n"
        "   - Else: Remove without substitution\n"
        "B) For structure errors:\n"
        "   - Trim excess items starting from last\n"
        "   - Never merge concepts\n\n"
        "Example Before/After:\n"
        "[BEFORE] Automation: '5. Automating payroll'\n"
        "[AFTER] Automation: (item 5 removed)\n\n"
        "Return ONLY this format:\n"
        "### Specific AI Tools\n"
        "- [Tool1]: [Usage]\n"
        "...\n\n"
        "### Automation Opportunities\n"
        "- [Task1]\n"
        "...\n\n"
        "### Efficiency Gains\n"
        "- [Benefit1]\n"
        "...\n\n"
        "### Risks to Consider\n"
        "- [Risk1]\n"
        "..."
    )
)

agent_job_description_validation = autogen.AssistantAgent(
    name="agent_job_description_validation",
    llm_config=llm_config,
    system_message=(
        "You are a senior HR quality control assistant. "
        "Your role is to validate and review a generated job description to ensure it is clear, complete, and "
        "professionally written.\n\n"
        "You must carefully check that:\n"
        "- The structure follows standard job description sections (title, summary, responsibilities, qualifications, "
        "etc.).\n"
        "- The tone is formal, professional, and appropriate for a business context.\n"
        "- The responsibilities and requirements are logically consistent and relevant to the job title.\n"
        "- There are no hallucinated, vague, or unrelated statements.\n"
        "- The output does not contain any formatting issues or missing sections.\n\n"
        "### Your response should include:\n"
        "- A list of any detected **issues or improvements**, each followed by a suggested correction (if needed).\n"
        "- If the job description is valid and well-written, respond with: `✅ The job description is valid and "
        "requires no changes.`\n\n"
        "Only respond in **English**, and do **not** rewrite the full description unless specific corrections are "
        "needed."
    )
)

agent_jd_corrector = autogen.AssistantAgent(
    name="agent_jd_corrector",
    llm_config=llm_config,
    system_message=(
        "You are a correction assistant that receives a generated job description and feedback from a validator "
        "agent.\n\n"
        "Your task is to do the following:\n"
        "1. Apply the validator’s feedback to improve or fix the job description.\n"
        "2. If the validator found no issues, keep the job description unchanged.\n\n"
        "Your output MUST follow these rules:\n"
        "- Return **only** a JSON object with the key `jd` containing the full corrected job description.\n"
        "- The job description must be in **clean and properly formatted plain text** (inside the JSON value).\n"
        "- Do NOT include any explanations, notes, or comments outside the JSON.\n"
        "- Always write the job description in **English**, regardless of the input language.\n"
        "- Respect the tone, structure, and role context of the original job description.\n"
        "- If only minor edits are needed, still return the full job description inside `jd`.\n"
        "- Do NOT invent new responsibilities or qualifications that weren’t present originally or suggested by the "
        "feedback.\n\n"
        "Example output format:\n"
        "```json\n"
        "{\"jd\": \"[Corrected/unchanged job description text here]\"}\n"
        "```"
    )
)


def get_last_relevant_message(all_messages,agent_names):
    for i in range(1, len(all_messages) + 1):
        message = all_messages[-i]
        if message["name"] in agent_names:
            return message["content"]
    return None


def generator_job_description_func(job_name:str):
    job_description_generator_message = (
        "You are a senior HR assistant specialized in job description writing.\n\n"
        f"Your task is to generate a clear, concise, and professional job description for the **{job_name}** role.\n"
        "Your response must:\n"
        "- Start with the full **Job Title** clearly stated at the top.\n"
        "- Be written in **formal business English**.\n"
        "- Be structured with the following sections: **Job Title**, **Summary**, **Responsibilities**, "
        "**Qualifications**, and optionally **Preferred Skills** or **Tools/Technologies**.\n"
        "- Reflect the job position accurately and tailor the description according to any provided context.\n"
        "- Do **not** fabricate unrealistic or overly generic responsibilities.\n"
        "- Do **not** include explanations, JSON, Markdown, or extra formatting—output should be clean plain text only.\n"
        "- Ensure the description reads like it was written by a professional HR department.\n\n"
        "Always include the job title explicitly in the output and make sure all sections are clearly differentiated."
    )
    groupchat_jd = GroupChat(
        agents=[agent_job_description_generator, agent_job_description_validation, agent_jd_corrector],
        messages=[],
        max_round=5
    )
    group_manager_jd = GroupChatManager(groupchat=groupchat_jd, llm_config=llm_config)
    user_proxy.initiate_chat(group_manager_jd, message=job_description_generator_message, silent=True)
    all_messages = group_manager_jd.groupchat.messages
    jd_text = get_last_relevant_message(all_messages,["agent_job_description_generator","agent_jd_corrector"])
    return jd_text


def extract_insight_jd_func(job_description:str):
    job_insight_message = (
        "You are a senior HR analyst specialized in job description processing.\n\n"
        "Your task is to analyze and extract structured insights from the following job description:\n"
        "--------------------------------------------------\n"
        f"{job_description}\n"
        "--------------------------------------------------\n\n"
        "Important Notes:\n"
        "- Work DIRECTLY from the provided text - do not modify or interpret the original content\n"
        "- If the description is incomplete or unclear, still extract what you can\n"
        "- Never invent information that isn't explicitly stated\n"
        "- Maintain all original terminology and phrasing where possible"
    )
    groupchat_jd = GroupChat(
        agents=[agent_extract_job_insights, agent_jd_critic, agent_jd_insight_fixer],
        messages=[],
        max_round=5
    )
    group_manager_jd = GroupChatManager(groupchat=groupchat_jd, llm_config=llm_config)
    user_proxy.initiate_chat(group_manager_jd, message=job_insight_message, silent=True)
    all_messages = group_manager_jd.groupchat.messages
    job_insight = get_last_relevant_message(all_messages,["agent_extract_job_insights","agent_jd_insight_fixer"])
    return job_insight


def ai_impact_jd_func(job_insight:str):
    job_impact_message = (
        "You are an AI transformation specialist analyzing how artificial intelligence could impact this role.\n\n"
        "### Job Insights Provided:\n"
        "--------------------------------------------------\n"
        f"{job_insight}\n"
        "--------------------------------------------------\n\n"
        "### Analysis Requirements:\n"
        "1. Base ALL suggestions ONLY on these specific insights\n"
        "2. Never invent capabilities not implied by the text\n"
        "3. Keep all recommendations practical and role-specific\n\n"
        "### Binding Constraints:\n"
        "- If insights mention tools/processes: suggest relevant AI enhancements\n"
        "- If insights are generic: recommend only widely applicable AI solutions\n"
        "- If insights are technical: match with equally technical AI tools\n"
        "- Return empty sections where no clear AI application exists"
    )
    groupchat_jd = GroupChat(
        agents=[agent_ai_impact_on_role, agent_ai_impact_validator, agent_ai_impact_corrector],
        messages=[],
        max_round=5
    )
    group_manager_jd = GroupChatManager(groupchat=groupchat_jd, llm_config=llm_config)
    user_proxy.initiate_chat(group_manager_jd, message=job_impact_message, silent=True)
    all_messages = group_manager_jd.groupchat.messages
    job_ia_impact = get_last_relevant_message(all_messages,["agent_ai_impact_on_role","agent_ai_impact_corrector"])
    return job_ia_impact


def ai_impact_role (job_position_name:str):
    job_description = generator_job_description_func(job_position_name)
    insight_jd = extract_insight_jd_func(job_description)
    impact_out_put = ai_impact_jd_func(insight_jd)
    return impact_out_put