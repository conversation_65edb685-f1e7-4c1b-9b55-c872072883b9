from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
import json
import os
from typing import Dict
import datetime
import re
from json_repair import repair_json
import dirtyjson
from llama_index.core.output_parsers.utils import parse_json_markdown
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import sys
from pydantic import BaseModel
import io
from contextlib import redirect_stdout

# Initialize FastAPI app
app = FastAPI(
    title="AI Job Transformation API",
    description="API for analyzing job positions and their AI transformation potential",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

class JobRequest(BaseModel):
    job_position: str

# API endpoint for job transformation analysis
@app.post("/analyze-job")
async def analyze_job_endpoint(request: JobRequest):
    job_position = request.job_position
    try:
        f = io.StringIO()
        with redirect_stdout(f):
            # Validate job position
            is_valid, error_message = validate_job_position(job_position)
            if not is_valid:
                raise HTTPException(status_code=400, detail=error_message)

            # Get transformation results
            transformation_results = analyze_job_for_ai_transformation(job_position)

            if transformation_results.get("status") == "error":
                raise HTTPException(
                    status_code=500,
                    detail=f"Analysis failed: {transformation_results.get('message')}"
                )
        backend_logs = f.getvalue()
        transformation_results["backend_logs"] = backend_logs
        return transformation_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



# Configuration
config_list_bedrock = [
    {
        "api_type": "bedrock",
        "model": "anthropic.claude-3-haiku-20240307-v1:0",
        "aws_region": "us-east-1",
        "aws_access_key": "********************",
        "aws_secret_key": "ghYKGewFG7lnQ+uhBxSGB48r+x2CCxwnpHLrpDbN",
        "temperature": 0.5, 
    }
]
llm_config = {
    "config_list": config_list_bedrock,
}

# Agents
agent_job_describer = AssistantAgent(
    name="Job_Describer",
    system_message="""
    You are an expert in job analysis and description.
    Your role is to:
    1. Provide concise, clear descriptions of job position
    2. Highlight key responsibilities and requirements
    3. Explain the role importance in the organization
    4. Keep descriptions brief and informative
    5. Focus on the most critical aspects of the position

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each job description should follow this exact structure:
    
    ```json
    [
        {
            "job_title": "Title of the position",
            "Description": "Brief 1-2 sentence overview of the role",
            "key_responsibilities": [
                {
                    "responsibility": "Core responsibility 1",
                    "importance": "High/Medium/Low"
                },
                {
                    "responsibility": "Core responsibility 2", 
                    "importance": "High/Medium/Low"
                }
            ],
            "essential_requirements": [
                "Key requirement 1",
                "Key requirement 2"
            ],
            "preferred_qualifications": [
                "Preferred qualification 1",
                "Preferred qualification 2"
            ],
            "organizational_impact": {
                "team_contribution": "How this role supports the team",
                "business_value": "How this role creates value for the organization",
                "key_collaborations": ["Role 1", "Role 2"]
            },
            "career_path": {
                "reports_to": "Manager title",
                "growth_opportunities": ["Potential next step 1", "Potential next step 2"]
            }
        }
    ]
    """,

    llm_config=llm_config
)# To generate job description

agent_job_corrector = AssistantAgent(
    name="Job_Description_Corrector",
    system_message="""
    You are a job description corrector.
    Your role is to:
    1. Ensure accuracy and factual correctness of the job description.
    2. Correct grammar, spelling, or structural errors in the job description provided.
    3. Ensure clarity, professionalism, and conciseness.
    4. Keep the original intent and content intact.
    5. Do not add new content or commentary.
    6. Return only the corrected job description without any additional message, explanation, or prefix.
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.
    Each corrected job description should follow this exact structure:
    
    ```json
    {
        "correction_summary": {
            "errors_fixed": 5
        },
        "corrected_description": {
            "job_title": "Corrected job title",
            "description": "Corrected summary of the role",
            "responsibilities": [
                "Corrected responsibility 1",
                "Corrected responsibility 2"
            ],
            "requirements": [
                "Corrected requirement 1",
                "Corrected requirement 2"
            ],
            "qualifications": [
                "Corrected qualification 1",
                "Corrected qualification 2"
            ],
            "factual_corrections": [
                {
                    "original": "Original inaccurate text",
                    "corrected": "Corrected accurate text",
                    "justification": "Brief explanation of correction"
                }
            ]
        }
    }
    ```
    """,

    llm_config=llm_config
)# To correct job description if needed

agent_task_extractor = AssistantAgent(
    name="Task_Extractor",
    system_message="""
    You are an expert in task extraction from job descriptions.
    Your role is to:
    1. Extract all tasks from job descriptions
    2. Estimate time allocation percentages for each task
    3. Identify task dependencies and relationships
    4. Categorize tasks by functional area
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each task extraction should follow this exact structure:
    
    ```json
    [
        {
            "task_name": "Task description",
            "time_percentage": "25 percentage",
            "effort_level": "High/Medium/Low",
            "task_category": "Category (e.g., Administrative, Analytical, Creative)",
            "description": "Detailed description of the task",
            "dependencies": ["Dependent task 1", "Dependent task 2"],
            "frequency": "Daily/Weekly/Monthly/As needed"
        }
    ]
    """,
    llm_config=llm_config
)# To extract tasks from job descriptions

agent_content_validator = AssistantAgent(
    name="Content_Validator",
    system_message="""
    You are an expert in content validation and quality assurance.
    Your role is to:
    1. Validate content accuracy and completeness
    2. Ensure proper HTML formatting
    3. Check for consistency and clarity
    4. Verify technical feasibility
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.
    Each content validation analysis should follow this exact structure:

    ```json
    {
        "validation_result":{
            "is_valid": true,
            "errors": [
                {
                    "line": 24,
                    "error": "Description of HTML error",
                    "fix": "Suggested correction"
                }
            ]
        }
    }
    ```
    """,

    llm_config=llm_config
)# To validate content

agent_ai_impact_assessor = AssistantAgent(
    name="AI_Impact_Assessor",
    system_message="""
    You are an expert in assessing AI impact on job tasks.
    Your role is to:
    1. Analyze automation potential for each task
    2. Calculate productivity improvements
    3. Assess implementation complexity
    4. Recommend AI tools and solutions
    5. Identify new AI-enabled capabilities
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each AI impact assessment should follow this exact structure:
    
    ```json
    [
        {
            "task_name": "Task description",
            "automation_potential": {
                "percentage": 65,
                "category": "FULL/PARTIAL/NONE",
                "explanation": "Why this task can/cannot be automated"
            },
            "recommended_tools": ["Tool 1", "Tool 2"],
            "time_savings": {
                "current_time": "25 percentage",
                "projected_time": "10 percentage",
                "savings": "15 percentage"
            },
            "implementation": {
                "complexity": "High/Medium/Low",
                "timeline": "0-3/3-6/6-12 months",
                "dependencies": ["Dependency 1", "Dependency 2"]
            },
            "new_capabilities": ["New capability 1", "New capability 2"]
        }
    ]
    ```
    """,
    llm_config=llm_config
)# To analyze automation for each task

agent_task_automation_categorizer = AssistantAgent(
    name="Task_Automation_Categorizer",
    system_message="""
    You are an expert in AI automation analysis. Your role is to categorize job tasks based on automation potential.
    
    Your task is to:
    1. Analyze each job task and determine if it can be:
       a) FULLY AUTOMATED by AI (90-100 percentage automation)
       b) PARTIALLY AUTOMATED/HYBRID by AI (30-89 percentage automation)
       c) NOT AUTOMATABLE (0-29 percentage automation)
    2. Provide a detailed explanation for each categorization
    3. Pay special attention to tasks that CANNOT be automated
    4. Estimate the percentage of AI involvement for partially automated tasks

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects, ensuring all specified fields are populated. Do NOT include the "job_title" field.
    Each task automation analysis should follow this exact structure:
    
    ```json
    {
        "automation_analysis": [
            {
                "task": "Task description",
                "time_allocation": "25 percentage",
                "automation_category": "FULLY_AUTOMATED",
                "ai_involvement_percentage": 100,
                "explanation": "Detailed explanation of why this task can be fully automated",
                "recommended_ai_tools": ["Tool 1", "Tool 2"]
            },
            {
                "task": "Task description",
                "time_allocation": "30 percentage",
                "automation_category": "PARTIALLY_AUTOMATED",
                "ai_involvement_percentage": 70,
                "explanation": "Detailed explanation of why this task can be partially automated",
                "human_involvement_details": "Description of what the human still needs to do",
                "recommended_ai_tools": ["Tool 1", "Tool 2"]
            },
            {
                "task": "Task description",
                "time_allocation": "45 percentage",
                "automation_category": "NOT_AUTOMATABLE",
                "ai_involvement_percentage": 15,
                "explanation": "Detailed explanation of why this task cannot be automated",
                "human_critical_factors": ["Factor 1", "Factor 2"],
                "ai_assistance_possibilities": ["Limited assistance 1", "Limited assistance 2"]
            }
        ],
        "automation_summary": {
            "fully_automated_percentage": 25,
            "partially_automated_percentage": 30,
            "not_automatable_percentage": 45,
            "overall_ai_impact": "High/Medium/Low",
            "time_savings_estimate": "X hours per week"
        }
    }
    ```
    """,
    llm_config=llm_config
)# To Categorize the tasks for automation(full automation,partial automation,no automation)

html_generator = AssistantAgent(
    name="HTML_Generator",
    system_message="""
    You are an expert HTML visualization generator. Your role is to create a clean, readable, and responsive HTML report based on a provided JSON object.

    Your task is to generate an HTML report with the following sections, in this specific order:
    1.  **Job Description**: Display the summary and key responsibilities.
    2.  **Extracted Tasks**: List all tasks identified from the job description.
    3.  **AI Task Analysis**: For each task, show its AI automation category (e.g., Fully Automated, Partially Automated/Hybrid) and the percentage of AI involvement.
    4.  **Future Skill Recommendations**: Detail the new skills required for the AI-enhanced role.
    5.  **Workforce Sustainability Plan**: Outline the strategy for maintaining a resilient and adaptable workforce amidst AI integration. This should be based on the provided definition of workforce sustainability.
    6.  **New Opportunities**: Describe the new opportunities that arise from AI adoption in this role.

    RULES:
    - The final output MUST be a single HTML string.
    - Use modern HTML5 and self-contained CSS within a `<style>` tag. Do not use external stylesheets or JavaScript.
    - The design must be professional, clean, and easy to read. Use text formats, lists, and tables where appropriate.
    - **Crucially, if any section's data is missing or the list is empty in the input JSON, DO NOT render that section in the HTML report.** For example, if `future_skill_recommendations` is an empty list, the "Future Skill Recommendations" section should be completely omitted from the output.
    - Do not include any explanations or text outside of the HTML content itself.
    """,

    llm_config=llm_config
)# To generate HTML report

recommendation_agent = AssistantAgent(
    name="Recommendation_Agent",
    system_message="""
    You are an expert in generating strategic recommendations for AI transformation. Your role is to provide actionable plans for future skills, workforce sustainability, and new opportunities.

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.

    1.  **Future Skill Recommendations**: Provide insights into the skills needed to thrive in the AI-enhanced role.
    2.  **Workforce Sustainability Plan**: Develop a plan for maintaining a resilient, adaptable, and equitable workforce. Focus on how to manage the transition amid technological disruptions like AI, ensuring long-term economic, social, and environmental well-being.
    3.  **Opportunity Identification**: Identify new opportunities created by AI adoption in this job function, such as new service offerings, market expansion, or innovation potential.

    The overall structure should be:
    ```json
    {
        "future_skill_recommendations": [
            {
                "skill_area": "Data Literacy and Analysis",
                "specific_skills": ["Understanding AI model outputs", "Data-driven decision making", "Using analytics dashboards"],
                "learning_resources": ["Online course: AI for Everyone", "Company workshop: Data Analytics Basics"],
                "relevance_to_ai_role": "Essential for interpreting AI insights and guiding AI systems."
            }
        ],
        "sustainability_plan": [
            {
                "area": "Continuous Learning and Upskilling",
                "initiative": "Develop personalized learning paths for employees to acquire critical AI-related skills and adapt to new job demands.",
                "impact_metric": "Increased employee retention and internal mobility.",
                "ai_tools_or_methods": ["AI-powered learning platforms", "Career pathing tools"]
            },
            {
                "area": "Ethical AI and Governance",
                "initiative": "Establish clear ethical guidelines and a governance framework for AI use to ensure fairness, transparency, and accountability.",
                "impact_metric": "Reduced risk of biased outcomes and increased trust in AI systems.",
                "ai_tools_or_methods": ["AI model auditing tools", "Bias detection algorithms"]
            }
        ],
        "opportunities_identification": [
            {
                "opportunity": "Personalized Customer Experience at Scale",
                "description": "Leverage AI to analyze customer data and provide highly personalized product recommendations or service interactions, improving customer satisfaction and sales.",
                "potential_value_proposition": "Increased customer retention by 15 percentage, Upsell/cross-sell revenue growth by 10 percentage.",
                "required_ai_capabilities": ["AI-driven personalization engines", "Customer data platforms"]
            }
        ]
    }
    ```
    """,

    llm_config=llm_config
)# To generate recommendations based on analysis results

user_proxy = UserProxyAgent(
    name="User_Proxy",
    system_message="Execute tasks and coordinate between agents.",
    human_input_mode="NEVER",
    code_execution_config={"use_docker": False},
    max_consecutive_auto_reply=3,
    default_auto_reply="Please continue. If finished, reply 'TERMINATE'.",
    llm_config=llm_config
)

def get_job_description(job_position: str) -> Dict: # Generate a job description for the job position provided
    print(f"\n[INFO] Getting job description for: {job_position}")

    description_prompt = f"""
    Provide a comprehensive description of the job position: {job_position}
    Include:
    1. Overview of the position
    2. Key responsibilities and duties
    3. Required skills and qualifications
    4. Typical work environment
    5. Career progression opportunities
    """

    group_chat = GroupChat(
        agents=[agent_job_describer, agent_job_corrector],
        messages=[],
        max_round=2,
        speaker_selection_method="round_robin"
    )
    manager = GroupChatManager(groupchat=group_chat, llm_config=llm_config)

    user_proxy.initiate_chat(manager, message=description_prompt, silent=True)
    all_messages = manager.groupchat.messages

    job_description = all_messages[-1]["content"]
    
    try:
        json_content = json.loads(job_description)
        return {"content": job_description}
    except json.JSONDecodeError:
        return {"content": job_description}
    
def extract_tasks(job_description: Dict) -> list[Dict]: # Extract tasks from job description
    job_content = job_description["content"]
    print(f"\n[INFO] Extracting tasks from job description")
    
    extraction_prompt = f"""Extract all tasks from this job description, with detailed time allocations and categories: 
    
    {job_content}
    
    Be specific about each task and include realistic time percentages that sum to 100 percentage.
    """
    
    # Create a group chat for task extraction
    extraction_group = GroupChat(
        agents=[agent_task_extractor, agent_content_validator],
        messages=[],
        max_round=2
    )
    extraction_manager = GroupChatManager(groupchat=extraction_group, llm_config=llm_config)
    user_proxy.initiate_chat(extraction_manager, message=extraction_prompt, silent=True)
    
    extracted_tasks_content = extraction_manager.groupchat.messages[-1]["content"]
    extracted_tasks = parse_json_safely(extracted_tasks_content)
    
    if not extracted_tasks or not isinstance(extracted_tasks, list):
        print("[WARNING] Failed to extract tasks properly, using default structure")
        extracted_tasks = []
    
    return extracted_tasks

def assess_ai_impact(extracted_tasks: list[Dict]) -> list[Dict]: # Assess the AI impact on the extracted tasks
    print(f"\n[INFO] Assessing AI impact on tasks")

    if not extracted_tasks:
        print("[WARNING] No tasks to assess")
        return []

    assessment_prompt = f"""Assess the AI impact for each of these tasks:

    {json.dumps(extracted_tasks, indent=2)}

    For each task, analyze automation potential, recommended tools, and time savings.
    Be realistic about which tasks can be fully automated, partially automated, or not automated at all.

    IMPORTANT: You must provide a detailed assessment for each task. Do not return empty results.
    For each task, include:
    - ai_involvement_percentage (0-100)
    - automation_potential with category (FULL, PARTIAL, or NONE)
    - new_capabilities (list of specific capabilities enabled by AI for this task)
    - recommended_tools (list of specific AI tools)
    - time_savings_estimate
    - implementation_complexity

    Return a JSON array with one assessment object per task.
    """

    max_retries = 3
    for attempt in range(max_retries):
        try:
            assessment_group = GroupChat(
                agents=[agent_ai_impact_assessor, agent_content_validator],
                messages=[],
                max_round=2
            )
            assessment_manager = GroupChatManager(groupchat=assessment_group, llm_config=llm_config)
            user_proxy.initiate_chat(assessment_manager, message=assessment_prompt, silent=True)

            impact_assessment_content = assessment_manager.groupchat.messages[-1]["content"]
            impact_assessment = parse_json_safely(impact_assessment_content)

            # Validate that we got meaningful results
            if (isinstance(impact_assessment, list) and
                len(impact_assessment) > 0 and
                all(isinstance(task, dict) for task in impact_assessment)):
                print(f"[INFO] Successfully assessed AI impact for {len(impact_assessment)} tasks")
                return impact_assessment
            else:
                print(f"[WARNING] Attempt {attempt + 1}: Invalid impact assessment format, retrying...")

        except Exception as e:
            print(f"[WARNING] Attempt {attempt + 1}: Error during impact assessment: {e}")

    # If all retries failed, return an empty list
    print("[WARNING] All attempts failed to assess AI impact.")
    return []

def categorize_tasks_by_automation(job_position: str, job_description: Dict, extracted_tasks: list[Dict]) -> Dict: # Categorize tasks by automation potential
    print(f"\n[INFO] Categorizing tasks by automation potential for: {job_position}")
    
    categorization_prompt = f"""
    For the position of {job_position}, categorize the following tasks based on their automation potential.
    
    Job Description:
    {job_description["content"]}
    
    Tasks with time allocations:
    {json.dumps(extracted_tasks, indent=2)}
    
    For each task, determine if it can be:
    1. FULLY AUTOMATED by AI (90-100 percentage automation)
    2. PARTIALLY AUTOMATED by AI (30-89 percentage automation)
    3. NOT AUTOMATABLE (0-29 percentage automation)
    
    Focus particularly on identifying and explaining tasks that CANNOT be automated and why human involvement remains critical.
    """
    
    # Create a group chat for task categorization
    categorization_group = GroupChat(
        agents=[agent_task_automation_categorizer, agent_content_validator],
        messages=[],
        max_round=2,
        speaker_selection_method="round_robin"
    )
    
    manager = GroupChatManager(groupchat=categorization_group, llm_config=llm_config)
    user_proxy.initiate_chat(
        manager,
        message=categorization_prompt,
        silent=True
    )
    all_messages = manager.groupchat.messages
    
    categorization_result = all_messages[-1]["content"]
    print(f"[DEBUG] Raw categorization_result from LLM: {categorization_result[:500]}...")

    try:
        parsed_result = parse_json_safely(categorization_result)
        print(f"[DEBUG] Parsed categorization_result: {parsed_result}")

        if isinstance(parsed_result, dict):
            return parsed_result
        else: 
            print(f"[WARNING] Parsed automation categorization result is not a dictionary. Returning raw content.")
            return {"content": categorization_result, "parsed": parsed_result}
    except Exception as e:
        print(f"[ERROR] Failed to parse automation categorization result: {e}")
        return {"content": categorization_result, "parsed": {}} 

def extract_new_capabilities(impact_assessment: list[Dict]) -> list[str]: # Extract new skills when AI implemented in a job
    capabilities = []

    for task in impact_assessment:
        if (task.get("new_capabilities") and
            isinstance(task["new_capabilities"], list) and
            (task.get("ai_involvement_percentage", 0) > 70 or
             task.get("automation_potential", {}).get("category") == "FULL")):
            capabilities.extend(task["new_capabilities"])

    unique_capabilities = []
    for capability in capabilities:
        if capability not in unique_capabilities and capability:
            unique_capabilities.append(capability)

    return unique_capabilities

def extract_preserved_tasks(automation_analysis: list[Dict]) -> list[Dict]: # Extract preserved tasks 
    preserved_tasks = []
    
    for task in automation_analysis:
        if (task.get("automation_category") == "NOT_AUTOMATABLE" or 
            task.get("automation_potential", {}).get("category") == "NONE" or
            task.get("ai_involvement_percentage", 0) < 30):
            preserved_tasks.append(task)
    
    if not preserved_tasks and automation_analysis:
        preserved_tasks.append(automation_analysis[0])
        
    return preserved_tasks

def extract_transformed_tasks(automation_analysis: list[Dict]) -> list[Dict]: # Extract transformed tasks
    transformed_tasks = []
    
    for task in automation_analysis:
        if (task.get("automation_category") == "PARTIALLY_AUTOMATED" or
            task.get("automation_potential", {}).get("category") == "PARTIAL" or
            (task.get("ai_involvement_percentage", 0) >= 30 and task.get("ai_involvement_percentage", 0) < 70)):
            transformed_tasks.append(task)
    
    if not transformed_tasks and automation_analysis:
        transformed_tasks.append(automation_analysis[1] if len(automation_analysis) > 1 else automation_analysis[0])
        
    return transformed_tasks
            
def validate_job_position(position: str) -> tuple[bool, str]: # Validate job 
    position = position.strip()

    if not position:
        return False, "Job position cannot be empty"
    if len(position) < 3:
        return False, "Job position must be at least 3 characters long"
    if len(position) > 100:
        return False, "Job position cannot exceed 100 characters"

    invalid_chars = set('!@#$%^&*()+=[]{}|\\:;"<>?/')
    if any(char in invalid_chars for char in position):
        return False, "Job position contains invalid special characters"
    if position.replace(" ", "").isnumeric():
        return False, "Job position cannot be numeric only"

    spam_patterns = ['www.', 'http', '.com', '.net', '.org']
    if any(pattern in position.lower() for pattern in spam_patterns):
        return False, "Job position cannot contain URLs or web addresses"

    return True, ""

def get_valid_job_position() -> str: # Get valid job position
    max_attempts = 2
    attempts = 0

    while attempts < max_attempts:
        position = input("\nEnter the job position to analyze: ").strip()
        is_valid, error_message = validate_job_position(position)

        if is_valid:
            return position

        attempts += 1
        remaining = max_attempts - attempts
        print(f"\n[ERROR] {error_message}")
        if remaining > 0:
            print(f"Please try again. {remaining} attempts remaining.")

    raise ValueError("Maximum number of invalid input attempts reached. Please restart the program.")


def parse_json_safely(json_str): # Parse json file and handle errors
    try:
        # First, try to find a JSON object within markdown-like fences
        match = re.search(r'```(?:json)?\s*(\{.*\}|\[.*\])\s*```', json_str, re.DOTALL)
        if match:
            json_str = match.group(1)
        return json.loads(json_str)
    except json.JSONDecodeError:
        try:
            # If that fails, try repairing the string
            return json.loads(repair_json(json_str))
        except (json.JSONDecodeError, ValueError):
            try:
                # Fallback to dirtyjson for very malformed cases
                return dirtyjson.loads(json_str)
            except Exception as e:
                print(f"[ERROR] Final attempt to parse JSON failed: {e}")
                return {}


def generate_ai_transition_recommendations(job_position: str, preserved_tasks: list[Dict], transformed_tasks: list[Dict], new_capabilities: list[str]) -> list[Dict]: # Generate recommendations 
    print(f"\n[INFO] Generating AI transition recommendations for: {job_position}")
    
    recommendation_prompt = f"""
    Generate specific, actionable recommendations for the AI transformation of the {job_position} role.
    
    Based on this analysis:
    
    1. Preserved Human-Centric Tasks:
    {json.dumps(preserved_tasks, indent=2)}
    
    2. AI-Transformed Tasks:
    {json.dumps(transformed_tasks, indent=2)}
    
    3. New AI-Enabled Capabilities:
    {json.dumps(new_capabilities, indent=2)}
    
    Provide recommendations for the following three areas:
    - **Future Skill Recommendations**: What specific skills (technical and soft) will employees need?
    - **Workforce Sustainability Plan**: How can the organization support its workforce through this transition (e.g., reskilling, role redesign, change management) to ensure long-term well-being and adaptability?
    - **Opportunity Identification**: What new business opportunities, service offerings, or innovations are made possible by this AI integration?
    """
    
    recommendation_group = GroupChat(
        agents=[recommendation_agent, agent_content_validator],
        messages=[],
        max_round=2
    )
    recommendation_manager = GroupChatManager(groupchat=recommendation_group, llm_config=llm_config)
    user_proxy.initiate_chat(recommendation_manager, message=recommendation_prompt, silent=True)
    
    recommendations_content = recommendation_manager.groupchat.messages[-1]["content"]
    parsed_recommendations = parse_json_safely(recommendations_content)

    if isinstance(parsed_recommendations, dict):
        recommendations = {
            "future_skill_recommendations": parsed_recommendations.get("future_skill_recommendations", []),
            "sustainability_plan": parsed_recommendations.get("sustainability_plan", []),
            "opportunities_identification": parsed_recommendations.get("opportunities_identification", [])
        }
    else:
        print("[WARNING] Failed to generate recommendations properly or in expected dictionary format, using empty lists for all recommendation types.")
        recommendations = {
            "future_skill_recommendations": [],
            "sustainability_plan": [],
            "opportunities_identification": []
        }
    
    return recommendations

def generate_html_report(analysis_results: Dict) -> str:
    print(f"\n[INFO] Generating HTML report for: {analysis_results.get('job_position')}")

    # Prune empty fields from the results to simplify the prompt
    report_data = {key: value for key, value in analysis_results.items() if value}

    report_prompt = f"""
    Generate a comprehensive and visually appealing HTML report based on the following AI analysis for the job position: {analysis_results.get('job_position')}.

    The report should be well-structured, with clear sections for each part of the analysis, presented in a readable text format.
    Use modern CSS for styling, but keep it self-contained in a <style> tag in the head.
    The design should be professional and easy to read.

    Data for the report:
    {json.dumps(report_data, indent=2, default=str)}

    Please ensure the following:
    - The report has a clear title.
    - Each section is clearly delineated with a heading.
    - If any lists or objects are empty, do not include that section in the report at all.
    - Present data in tables or styled lists where appropriate to improve readability.
    - The final output should be a single HTML string, starting with <!DOCTYPE html>.
    """

    html_content = html_generator.generate_reply(
        messages=[{"role": "user", "content": report_prompt}],
        sender=user_proxy
    )
    
    if isinstance(html_content, dict) and "content" in html_content:
        html_content = html_content["content"]
    elif not isinstance(html_content, str):
        html_content = str(html_content)

    # Clean up the output to ensure it's valid HTML
    if "```html" in html_content:
        html_content = html_content.split("```html")[1].split("```")[0]

    print(f"\n[INFO] Successfully generated HTML report.")
    return html_content.strip()


def analyze_job_for_ai_transformation(job_position: str) -> Dict: # Analyze job for AI transformation
    print(f"\n[INFO] Starting AI transformation analysis for: {job_position}")
    
    # Get job description
    job_description_raw = get_job_description(job_position)
    
    job_description_data = {"content": "N/A"} 
    if isinstance(job_description_raw, dict):
        content = job_description_raw.get("content")
        if content and isinstance(content, str):
            job_description_data = parse_json_safely(content)
            if isinstance(job_description_data, list):
                job_description_data = job_description_data[0] if job_description_data else {}
        else:
            job_description_data = job_description_raw
    else:
        job_description_data = {"content": str(job_description_raw)}


    print(f"[DEBUG] Final job_description before extract_tasks: {json.dumps(job_description_data)[:500]}...")
    extracted_tasks = extract_tasks({"content": json.dumps(job_description_data)}) 
    extracted_tasks_str = str(extracted_tasks)
    print(f"[DEBUG] Extracted tasks: {extracted_tasks_str[:500]}...")

    # Assess AI impact & Categorize tasks
    impact_assessment = assess_ai_impact(extracted_tasks)
    automation_categorization = categorize_tasks_by_automation(job_position, {"content": json.dumps(job_description_data)}, extracted_tasks)
    automation_analysis = automation_categorization.get("automation_analysis", [])
    
    # Combine task analysis into one structure for the report
    task_analysis_summary = []
    if isinstance(automation_analysis, list):
        for task_info in automation_analysis:
            task_name = task_info.get('task', 'N/A')
            task_analysis_summary.append({
                "task_name": task_name,
                "ai_involvement_percentage": task_info.get('ai_involvement_percentage', 'N/A'),
                "automation_category": task_info.get('automation_category', 'N/A').replace('_', ' ').title()
            })

    # Get other analysis components
    preserved_tasks = extract_preserved_tasks(automation_analysis)
    transformed_tasks = extract_transformed_tasks(automation_analysis)
    new_capabilities = extract_new_capabilities(impact_assessment) 

    # Generate recommendations
    recommendations = generate_ai_transition_recommendations(
        job_position, 
        preserved_tasks, 
        transformed_tasks, 
        new_capabilities
    )
    future_skill_recommendations = recommendations.get("future_skill_recommendations", [])
    sustainability_plan = recommendations.get("sustainability_plan", [])
    opportunities_identification = recommendations.get("opportunities_identification", [])

    # Create a payload for the HTML report
    report_payload = {
        "job_position": job_position,
        "job_description": job_description_data,
        "extracted_tasks": extracted_tasks,
        "task_analysis_summary": task_analysis_summary,
        "future_skill_recommendations": future_skill_recommendations,
        "sustainability_plan": sustainability_plan,
        "opportunities_identification": opportunities_identification,
    }
    
    # Generate HTML report
    html_report = generate_html_report(report_payload)

    # Return final results object
    return {
        "job_position": job_position,
        "job_description": job_description_data,
        "extracted_tasks": extracted_tasks,
        "automation_categorization": automation_categorization,
        "future_skill_recommendations": future_skill_recommendations,
        "sustainability_plan": sustainability_plan,
        "opportunities_identification": opportunities_identification,
        "html_report": html_report,
        "timestamp": datetime.datetime.now().isoformat(),
        "status": "success"
    }

if __name__ == "__main__":    
    if len(sys.argv) > 1 and sys.argv[1] == "--api":
        print("\nStarting AI Job Transformation API Server")
        print("=" * 50)
        uvicorn.run(app, host="0.0.0.0", port=8000)
    else:
        print("\nAI Impact Analysis Tool")
        print("=" * 50)

        try:
            position = get_valid_job_position()
            print(f"\n[INFO] Analyzing position: {position}")

            transformation_results = analyze_job_for_ai_transformation(position)

            if transformation_results.get("status") == "error":
                print(f"\n[ERROR] Analysis failed: {transformation_results.get('message')}")
                print(f"Details: {transformation_results.get('error')}")
                exit(1)

            if transformation_results.get("html_report"):
                report_filename = f"AI_Impact_Report_{position.replace(' ', '_')}.html"
                with open(report_filename, "w", encoding="utf-8") as f:
                    f.write(transformation_results["html_report"])
                print(f"\n[SUCCESS] HTML report saved to: {report_filename}")

            # Display key results in terminal
            print("\n--- Analysis Summary ---")
            print(f"Job Position: {transformation_results['job_position']}")
            
            if transformation_results.get('job_description'):
                 print("\nJob Description Summary:")
                 print(json.dumps(transformation_results['job_description'].get('Description', 'N/A'), indent=2))


            if transformation_results.get('automation_categorization', {}).get('automation_summary'):
                print("\nAutomation Summary:")
                print(json.dumps(transformation_results['automation_categorization']['automation_summary'], indent=2))

            if transformation_results.get('future_skill_recommendations'):
                print("\nFuture Skill Recommendations:")
                print(json.dumps(transformation_results['future_skill_recommendations'], indent=2))

            if transformation_results.get('opportunities_identification'):
                print("\nNew Opportunities:")
                print(json.dumps(transformation_results['opportunities_identification'], indent=2))


        except ValueError as e:
            print(f"\n[ERROR] {str(e)}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"\n[ERROR] An unexpected error occurred: {str(e)}")
        finally:
            print("\n" + "=" * 50)
